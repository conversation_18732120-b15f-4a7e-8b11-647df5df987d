<template>
    <el-form-item :label="label" :prop="propName">
      <el-select
        v-model="selectedCarrierId"
        @change="handleCarrierChange"
        filterable
        remote
        reserve-keyword
        :remote-method="remoteMethod"
        :placeholder="placeholder"
        class="!w-240px"
      >
        <el-option v-for="item in options" :key="item.id" :label="item.carrierName" :value="item.id" />
      </el-select>
    </el-form-item>
  </template>
  
  <script setup>
  import { ref, onMounted, watch } from 'vue'
  import { ElMessage } from 'element-plus'
  import { TransferCarrierApi } from '@/api/system/transfercarrier'
  
  // 定义选项列表
  const options = ref([])
  const selectedCarrierId = ref(null) // 用于存储选中的网点ID
  const loading = ref(false) // 用于控制加载状态
  const list = ref([]) // 用于存储从服务器获取的数据
  
  // 定义组件接收的外部属性
  const props = defineProps({
    propName: {
      type: String,
      required: true
    },
    modelValue: {
      type: [String, Number],
      default: null
    },
    label: {
      type: String,
      default: '网点选择'
    },
    placeholder: {
      type: String,
      default: ''
    }
  })
  
  const emit = defineEmits(['update:modelValue', 'carrier-selected'])
  
  // 远程搜索方法
  const remoteMethod = (query) => {
    if (query) {
      loading.value = true
      setTimeout(() => {
        loading.value = false
        options.value = list.value.filter((item) => {
          return item.carrierName.toLowerCase().includes(query.toLowerCase())
        })
      }, 200)
    } else {
      options.value = []
    }
  }
  
  // 在组件挂载完成后获取网点列表
  onMounted(async () => {
    try {
      const response = await TransferCarrierApi.getTransferCarrierList()
      console.log('获取数据成功:', response)
  
      list.value = response || []
  
      console.log(list.value)
      initSelectedCarrier(props.modelValue)
    } catch (error) {
      console.error('获取数据失败:', error)
      ElMessage.error('获取数据失败，请稍后再试！')
    }
  })
  
  // 初始化 selectedCarrierId
  const initSelectedCarrier = (id) => {
    if (id) {
      const selectedCarrier = list.value.find((item) => item.id === id)
      if (selectedCarrier) {
        selectedCarrierId.value = id
      }
    }
  }
  
  // 监听 modelValue 变化并更新 selectedCarrierId
  watch(
    () => props.modelValue,
    (newVal) => {
      selectedCarrierId.value = newVal
      initSelectedCarrier(newVal)
    }
  )
  
  const handleCarrierChange = (value) => {
    const selectedCarrier = list.value.find((item) => item.id === value)
    if (selectedCarrier) {
  
      emit('update:modelValue', value)
      emit('carrier-selected', {
        carrierId: selectedCarrier.id,
        carrierName: selectedCarrier.carrierName,
        carrierTel: selectedCarrier.managerTel
      })
    }
  }
  </script>