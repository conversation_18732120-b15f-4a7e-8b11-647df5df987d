<template>
  <div class="flex flex-row items-center gap-2">
    <el-radio-group v-model="shortcutDays" @change="handleShortcutDaysChange">
      <el-radio-button :label="1">昨天</el-radio-button>
      <el-radio-button :label="7">最近7天</el-radio-button>
      <el-radio-button :label="30">最近30天</el-radio-button>
    </el-radio-group>
    <el-date-picker
      v-model="times"
      value-format="YYYY-MM-DD HH:mm:ss"
      type="daterange"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]"
      :shortcuts="shortcuts"
      class="!w-240px"
      @change="emitDateRangePicker"
    />
    <slot></slot>
  </div>
</template>
<script lang="ts" setup>
import dayjs from 'dayjs'
import * as DateUtil from '@/utils/formatTime'

/** 快捷日期范围选择组件 */
defineOptions({ name: 'ShortcutDateRangePicker' })

const shortcutDays = ref(7) // 日期快捷天数（单选按钮组）, 默认7天
const times = ref<[string, string]>(['', '']) // 时间范围参数
defineExpose({ times }) // 暴露时间范围参数
/** 日期快捷选择 */
const shortcuts = [
  {
    text: '昨天',
    value: () => DateUtil.getDayRange(new Date(), -1)
  },
  {
    text: '最近7天',
    value: () => DateUtil.getLast7Days()
  },
  {
    text: '本月',
    value: () => [dayjs().startOf('M'), dayjs().subtract(1, 'd')]
  },
  {
    text: '最近30天',
    value: () => DateUtil.getLast30Days()
  },
  {
    text: '最近1年',
    value: () => DateUtil.getLast1Year()
  }
]

/** 设置时间范围 */
function setTimes() {
  const beginDate = dayjs().subtract(shortcutDays.value, 'd')
  const yesterday = dayjs().subtract(1, 'd')
  times.value = DateUtil.getDateRange(beginDate, yesterday)
}

/** 快捷日期单选按钮选中 */
const handleShortcutDaysChange = async () => {
  // 设置时间范围
  setTimes()
  // 发送时间范围选中事件
  await emitDateRangePicker()
}

/** 触发事件：时间范围选中 */
const emits = defineEmits<{
  (e: 'change', times: [dayjs.ConfigType, dayjs.ConfigType]): void
}>()
/** 触发时间范围选中事件 */
const emitDateRangePicker = async () => {
  emits('change', times.value)
}

/** 初始化 **/
onMounted(() => {
  handleShortcutDaysChange()
})
</script>
