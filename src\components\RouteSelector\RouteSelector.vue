<template>
  <el-form-item :label="label" :prop="propName">
    <el-select
      v-model="selectedBranchId"
      @change="handleBranchChange"
      filterable
      remote
      reserve-keyword
      :remote-method="remoteMethod"
      :placeholder="placeholder"
      :class="selectClass"
      :disabled="selectDisabled"
    >
      <el-option v-for="item in options" :key="item.id" :label="item.discDeptName" :value="item.discDeptId" />
    </el-select>
  </el-form-item> 
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import * as DeptApi from '@/api/system/dept'
import { RouteApi } from '@/api/system/route'

// 定义选项列表
const options = ref([])
const selectedBranchId = ref(null) // 用于存储选中的网点ID
const loading = ref(false) // 用于控制加载状态
const list = ref([]) // 用于存储从服务器获取的数据

// const loginDept = ref()

// 定义组件接收的外部属性
const props = defineProps({
  propName: {
    type: String,
    required: true
  },
  modelValue: {
    type: [String, Number],
    default: null
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  selectClass: {
    type: String,
    default: '!w-240px'
  },
  selectDisabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'branch-selected'])

// 远程搜索方法
const remoteMethod = (query) => {
  if (query) {
    loading.value = true
    setTimeout(() => {
      loading.value = false
      options.value = list.value.filter((item) => {
        return item.discDeptName.toLowerCase().includes(query.toLowerCase())
      })
    }, 200) 
  } else {
    options.value = []
  }
}

// 在组件挂载完成后获取网点列表
onMounted(async () => {
  try {
    // 获取登录用户的部门信息
    const loginDept = await DeptApi.getLoginUserDept();

    // 确保 loginDept 查询完成后再执行 getRouteByDeptId
    const response = await RouteApi.getRouteByDeptId(loginDept.id);

    list.value = response || [];
    options.value = list.value;

  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败，请稍后再试！')
  }
})

//监听 modelValue 变化并更新 selectedBranchId
watch(
  () => props.modelValue,
  (newVal) => {
    selectedBranchId.value = newVal
  }
)

const handleBranchChange = (value) => {
  const selectedBranch = list.value.find((item) => item.discDeptId === value)
  if (selectedBranch) {
    emit('update:modelValue', value)
    emit('branch-selected', {
      branchId: selectedBranch.discDeptId,
      branchName: selectedBranch.discDeptName
    })
  }
}
</script>






  