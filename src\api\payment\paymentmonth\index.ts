import request from '@/config/axios'

// 回单付账单 VO
export interface MonthVO {
  id: number // id
  paymentId: string // 账单id
  paymentNo: string // 账单编号
  customerName: string // 姓名
  customerNumber: string // 会员号
  customerMobile: string // 电话
  financeDeptId: number // 收款部门id
  financeDeptName: string // 收款部门名称
  finaceDeptUser: string // 收款人员
  balanceTime: Date // 账单日期
  totalExpenses: number // 账单金额
  paidAmount: number // 支付金额
  remainAmount: number // 剩余金额
  isAudit: number // 是否审核
  auditTime: Date // 结算时间
  auditUser: string // 结算人员
  revokeUser: string // 反结算人员
  revokeTime: Date // 反结算时间
}

// 回单付账单 API
export const MonthApi = {
  // 查询回单付账单分页
  getMonthPage: async (params: any) => {
    return await request.get({ url: `/payment/month/page`, params })
  },

  // 查询回单付账单详情
  getMonth: async (id: number) => {
    return await request.get({ url: `/payment/month/get?id=` + id })
  },

  // 新增回单付账单
  createMonth: async (data: MonthVO) => {
    return await request.post({ url: `/payment/month/create`, data })
  },

  // 修改回单付账单
  updateMonth: async (data: MonthVO) => {
    return await request.put({ url: `/payment/month/update`, data })
  },

  // 删除回单付账单
  deleteMonth: async (id: number) => {
    return await request.delete({ url: `/payment/month/delete?id=` + id })
  },

  //批量删除回款单
  batchDeleteMonth: async (ids: number[]) => {
    return await request.delete({ url: `/payment/month/batch-delete`, data: ids })
  },

  // 导出回单付账单 Excel
  exportMonth: async (params) => {
    return await request.download({ url: `/payment/month/export-excel`, params })
  },
  /*结算账单*/
  settleMonth: async (id: number)=> {
    return request.post({ url: `/payment/month/settle?id=` + id })
  },
  /*反结算账单*/
  revokeSettleMonth: (id: number)=> {
    return request.post({ url: `/payment/month/revoke-settle?id=` + id })

  },
  addPayment: (data: MonthVO) =>{
    console.log(data)
    return request.post({ url: `/payment/month/add-payment`, data })
  }
}
