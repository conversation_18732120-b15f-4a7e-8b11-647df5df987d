import request from '@/config/axios'

// 运单理赔 VO
export interface OrderClaimVO {
  id: number // 编号
  waybillId: number // 运单ID
  waybillNo: string // 运单编号
  claimStatus: number // 理赔状态
  claimType: number // 理赔类型
  applyId: number // 申请人ID
  applyOperationId: number // 申请操作人ID
  applyOperationName: string // 申请操作人名称
  applyTime: Date // 申请时间
  applyReason: string // 申请原因
  applyAmount: number // 申请金额
  applyImgUrl: string // 申请图片
  revokeId: number // 撤销人ID
  revokeName: string // 撤销人名称
  revokeTime: Date // 撤销时间
  revokeReason: string // 撤销原因
  approvalId: number // 审批人ID
  approvalName: string // 审批人名称
  approvalTime: Date // 审批时间
  remark: string // 备注
  handleId: number // 处理人ID
  handleName: string // 处理人名称
  handleTime: Date // 处理时间
  payer: string // 付款人
  payTime: Date // 付款时间
  payReason: string // 付款备注
  payAmount: number // 赔付金额
  payType: number // 付款方式
  collectPayId: number // 收款人编号
  endId: number // 结束人ID
  endName: string // 结束人名称
  endTime: Date // 结束时间
  interceptStatus: number // 是否拦截运单
  errorNum: number //异常件数
}

// 运单理赔 API
export const OrderClaimApi = {
  // 查询运单理赔分页
  getOrderClaimPage: async (params: any) => {
    return await request.get({ url: `/oems/order-claim/page`, params })
  },

  // 查询运单理赔详情
  getOrderClaim: async (id: number) => {
    return await request.get({ url: `/oems/order-claim/get?id=` + id })
  },

  // 新增运单理赔
  createOrderClaim: async (data: OrderClaimVO) => {
    return await request.post({ url: `/oems/order-claim/create`, data })
  },

  // 修改运单理赔
  updateOrderClaim: async (data: OrderClaimVO) => {
    return await request.put({ url: `/oems/order-claim/update`, data })
  },

  // 删除运单理赔
  deleteOrderClaim: async (id: number) => {
    return await request.delete({ url: `/oems/order-claim/delete?id=` + id })
  },

  // 导出运单理赔 Excel
  exportOrderClaim: async (params) => {
    return await request.download({ url: `/oems/order-claim/export-excel`, params })
  },

// ==================== 子表（理赔回复） ====================

  // 获得理赔回复列表
  getClaimRecordsListByOrderClaimId: async (orderClaimId) => {
    return await request.get({ url: `/oems/order-claim/claim-records/list-by-order-claim-id?orderClaimId=` + orderClaimId })
  },

// ==================== 子表（理赔责任） ====================

  // 获得理赔责任列表
  getClaimLiableListByOrderClaimId: async (orderClaimId) => {
    return await request.get({ url: `/oems/order-claim/claim-liable/list-by-order-claim-id?orderClaimId=` + orderClaimId })
  },
}
