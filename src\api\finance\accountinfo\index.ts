import request from '@/config/axios'

// 账户 VO
export interface AccountInfoVO {
  id: number // 自增序列
  accountRole: string // 账户角色（1- 平台 2-网点 3-物流公司）
  mainId: number // 网点id/物流公司id
  accountType: string // 账户类型
  overAllowAmount: number // 允许超出的额度（到付授权额度）
  totalAmount: number // 总金额
  availableAmount: number // 可用余额
  freezeAmount: number // 冻结金额
}

/**
 * 总账户 - TOTAL
 * （网点）运费预付账户 - NETWORK_FREIGHT_PRE
 * （网点）运费分成账户 - NETWORK_FREIGHT_DIVIDE
 * （网点）红包账户 - NETWORK_RED_PACKET
 * （网点）税费账户 - NETWORK_TAX
 * （网点）佣金账户 - NETWORK_BROKERAGE
 * （网点）保证金账户 - NETWORK_DEPOSIT
 * （物流公司）运费账户 - COMPANY_FREIGHT
 * （物流公司）税费账户 - COMPANY_TAX
 * （物流公司）佣金账户 - COMPANY_BROKERAGE
 * 平台运费账户 - PLATFORM_FREIGHT
 * 平台毛利账户 - PLATFORM_PROFIT
 * 平台营销账户 - PLATFORM_MARKET
 * 平台税费账户 - PLATFORM_TAX
 * 平台佣金账户 - PLATFORM_BROKERAGE
 */
export const ACCOUNT_TYPE_LIST  = reactive([
  { role: '', label: '总账户', value: 'TOTAL' },
  { role: '2', label: '运费预付账户', value: 'NETWORK_FREIGHT_PRE' },
  { role: '2', label: '运费分成账户', value: 'NETWORK_FREIGHT_DIVIDE' },
  { role: '2', label: '红包账户', value: 'NETWORK_RED_PACKET' },
  { role: '2', label: '税费账户', value: 'NETWORK_TAX' },
  { role: '2', label: '佣金账户', value: 'NETWORK_BROKERAGE' },
  { role: '2', label: '保证金账户', value: 'NETWORK_DEPOSIT' },
  { role: '3', label: '运费账户', value: 'COMPANY_FREIGHT' },
  { role: '3', label: '税费账户', value: 'COMPANY_TAX' },
  { role: '3', label: '佣金账户', value: 'COMPANY_BROKERAGE' },
  { role: '1', label: '运费账户', value: 'PLATFORM_FREIGHT' },
  { role: '1', label: '毛利账户', value: 'PLATFORM_PROFIT' },
  { role: '1', label: '营销账户', value: 'PLATFORM_MARKET' },
  { role: '1', label: '税费账户', value: 'PLATFORM_TAX' },
  { role: '1', label: '佣金账户', value: 'PLATFORM_BROKERAGE' }
 ])

export const getAccountTypeLabel = (type: string) => {
  if (!type) {
    return ''
  }
  return ACCOUNT_TYPE_LIST.find(item => item.value === type)?.label
}

/** 划转忽略的账户类型 */
const transferIgnoreList = reactive([
  // 总账户、（网点）税费账户、（物流公司）税费账户
  'TOTAL', 'NETWORK_TAX', 'COMPANY_TAX'
])
/**
 * 获取划转账户列表
 * @param id 账户id
 * @param accountRole 账户角色
 * @param mainId 网点/物流公司id
 * @returns 划转账户列表
 */
export const getTransferAccountList = async (id: number, accountRole: string, mainId: number) => {
  const accountList = await AccountInfoApi.listSingle({ accountRole: accountRole, mainId: mainId })
  // 返回 划转忽略账户类型 和 id 之外的数据
  return accountList.filter((item: any) => {
    return !transferIgnoreList.includes(item.accountType) && item.id !== id
  })
}

/**
 * 账户角色（1- 平台 2-网点 3-物流公司 ）
 */
export const ACCOUNT_ROLE_LIST  = reactive([
  { label: '平台', value: '1' },
  { label: '网点', value: '2' },
  { label: '物流公司', value: '3' }
])
export const getAccountRoleLabel = (type: string) => {
  if (!type) {
    return ''
  }
  return ACCOUNT_ROLE_LIST.find(item => item.value === type)?.label
}

/**
 * 结算状态（0-未结算 1-结算中 2-已结算 3-已审核 4-已归档 5-划转中 6-已划转）
 */
export const SETTLE_STATUS_LIST  = reactive([
  { label: '未结算', value: '0' },
  { label: '结算中', value: '1' },
  { label: '已结算', value: '2' },
  { label: '已审核', value: '3' },
  { label: '已归档', value: '4' },
  { label: '划转中', value: '5' },
  { label: '已划转', value: '6'}
])
export const getSettleStatusLabel = (type: string) => {
  if (!type) {
    return ''
  }
  return SETTLE_STATUS_LIST.find(item => item.value === type)?.label
}

/**
 * 平台收款码
 */
export const getPlatformReceiveQrCode = (amount: string) => {
  /*if (amount === '2000') {
        return 'https://dachisc.wang:9000/wljh-sanqian/f73a5edcadbda9770ee8e3d852abb92281679fc7278351b646b78d892993dbb8.jpg'
    } else if (amount === '5000') {
        return 'https://dachisc.wang:9000/wljh-sanqian/fa7220441a55708afb96b6d383b138dd97b8c0211e788dd592e62ba9e80c2c79.jpg'
    } else if (amount === '8000') {
        return 'https://dachisc.wang:9000/wljh-sanqian/26350844934bd7af141b4b4b18797793e5c106c4cff5fc2b620d3d2effda79e5.jpg'
    } else if (amount === '10000') {
        return 'https://dachisc.wang:9000/wljh-sanqian/023c7b9d310d6cebc09dded2b2fbb3b0f39f11caac23a8bd4bfbf2aefc156836.jpg'
    } else {
        return ''
    }*/
  if (amount === '500') {
    return 'https://dachisc.wang:9000/wljh-sanqian/ac9e0a3bd6f731e08fdcf915abcdc5beeba40f47a347bad20d11d7ed47362a37.jpg'
  } else if (amount === '1000') {
    return 'https://dachisc.wang:9000/wljh-sanqian/f29b767262e650e45fbc53445a06db24e76a9bebb01b87f749d563b7fadbb96e.jpg'
  } else if (amount === '2000') {
    return 'https://dachisc.wang:9000/wljh-sanqian/f73a5edcadbda9770ee8e3d852abb92281679fc7278351b646b78d892993dbb8.jpg'
  } else if (amount === '3000') {
    return 'https://dachisc.wang:9000/wljh-sanqian/bdac73eb40b5b82046788ea0974cb0500e2b421a862d40cc9800b2a245050f68.jpg'
  } else if (amount === '5000') {
    return 'https://dachisc.wang:9000/wljh-sanqian/fa7220441a55708afb96b6d383b138dd97b8c0211e788dd592e62ba9e80c2c79.jpg'
  } else {
    return ''
  }
}

// 账户 API
export const AccountInfoApi = {
  // 查询账户分页
  getAccountInfoPage: async (params: any) => {
    return await request.get({ url: `/wljh/account-info/page`, params })
  },

  // 查询账户详情
  getAccountInfo: async (id: number) => {
    return await request.get({ url: `/wljh/account-info/get?id=` + id })
  },

  // 新增账户
  createAccountInfo: async (data: AccountInfoVO) => {
    return await request.post({ url: `/wljh/account-info/create`, data })
  },

  // 修改账户
  updateAccountInfo: async (data: AccountInfoVO) => {
    return await request.put({ url: `/wljh/account-info/update`, data })
  },

  // 删除账户
  deleteAccountInfo: async (id: number) => {
    return await request.delete({ url: `/wljh/account-info/delete?id=` + id })
  },

  // 导出账户 Excel
  exportAccountInfo: async (params) => {
    return await request.download({ url: `/wljh/account-info/export-excel`, params })
  },

  // 网点账户分页
  pageNetwork: async (params: any) => {
    return await request.get({ url: `/wljh/account-info/pageNetwork`, params })
  },
  // 物流公司账户分页
  pageCompany: async (params: any) => {
    return await request.get({ url: `/wljh/account-info/pageCompany`, params })
  },
  // 平台账户列表
  listPlatform: async () => {
    return await request.get({ url: `/wljh/account-info/listPlatform` })
  },
  // 我的网点账户列表
  listMyNetwork: async () => {
    return await request.get({ url: `/wljh/account-info/listMyNetwork` })
  },
  // 我的物流公司账户列表
  listMyCompany: async () => {
    return await request.get({ url: `/wljh/account-info/listMyCompany` })
  },
  // 单部门账户列表
  listSingle: async (params: any) => {
    return await request.get({ url: `/wljh/account-info/listSingle`, params })
  },
  // 获取提现收款码
  getMainQrCode: async (accountRole: string, mainId: number) => {
    return await request.get({ url: `/wljh/account-info/getMainQrCode?accountRole=` + accountRole + `&mainId=` + mainId })
  },
  // 冻结
  freezeAccount: async (data: any) => {
    return await request.post({ url: `/wljh/account-info/freeze`, data })
  },
  // 解冻
  unFreezeAccount: async (data: any) => {
    return await request.post({ url: `/wljh/account-info/unFreeze`, data })
  },
  // 调账
  adjustAccount: async (data: any) => {
    return await request.post({ url: `/wljh/account-info/adjust`, data })
  },
  // 划转
  transferAccount: async (data: any) => {
    return await request.post({ url: `/wljh/account-info/transfer`, data })
  },
}
