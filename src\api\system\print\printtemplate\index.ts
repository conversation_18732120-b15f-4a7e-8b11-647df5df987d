import request from '@/config/axios'

// 打印模板配置 VO
export interface PrintTemplateVO {
  id: number // 自增序列
  name: string // 模板名称
  type: string // 模板类型
  clientType: string // 客户端类型
  printSettingId: number // 打印机配置表ID
  content: string // 模板内容
  isEnable: string // 是否启用：0 否 1 是
  ascription: string // 1：系统模板 2：自定义模板
  account: string // 系统模板时为空，自定义时必填
  printerType: string // 打印类型
}

// 打印模板配置 API
export const PrintTemplateApi = {
  // 查询打印模板配置分页
  getPrintTemplatePage: async (params: any) => {
    return await request.get({ url: `/wljh/print-template/page`, params })
  },

  // 查询打印模板配置详情
  getPrintTemplate: async (id: number) => {
    return await request.get({ url: `/wljh/print-template/get?id=` + id })
  },

  // 新增打印模板配置
  createPrintTemplate: async (data: PrintTemplateVO) => {
    return await request.post({ url: `/wljh/print-template/create`, data })
  },

  // 修改打印模板配置
  updatePrintTemplate: async (data: PrintTemplateVO) => {
    return await request.put({ url: `/wljh/print-template/update`, data })
  },

  // 删除打印模板配置
  deletePrintTemplate: async (id: number) => {
    return await request.delete({ url: `/wljh/print-template/delete?id=` + id })
  },

  // 导出打印模板配置 Excel
  exportPrintTemplate: async (params) => {
    return await request.download({ url: `/wljh/print-template/export-excel`, params })
  },

  // 新增打印模板配置
  addPrintTemplate: async (data: any) => {
    return await request.post({ url: `/wljh/print-template/add`, data })
  },

  // 获取打印模板控件信息
  getControlInfoAll: async (id: number) => {
    return await request.get({ url: `/wljh/print-template/getTemplateControl?id=` + id })
  },

  // 修改打印模板配置
  editPrintTemplate: async (data: any) => {
    return await request.put({ url: `/wljh/print-template/edit`, data })
  }
}
