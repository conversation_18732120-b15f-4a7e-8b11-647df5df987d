import request from '@/config/axios'

// 回单付收支信息 VO
export interface ReceiptPendingVO {
  id: number // id
  chargeId: string // 费用ID
  paymentId: string // 账单ID
  chargeCode: string // 费用代码
  chargeName: string // 费用名称
  chargeRefId: number // 运单号ID
  chargeRefNo: string // 运单号
  chargeType: string // 费用类型
  chargeTypeValue: string // 费用类型名称；例如：主营业务等
  chargeAmount: number // 金额
  chargeDate: Date // 费用产生日期
  receDeptId: number // 收款部门ID
  receDeptName: string // 收款部门名称
  chargeStatus: boolean // 回款状态 1：已回款 0：未回款
  shipper: string // 发货人
  shipperMobile: string // 发货人电话
  itemDesc: string // 物品描述
  deptId: number // 部门ID
  deptName: string // 部门名称

}

// 回单付收支信息 API
export const ReceiptPendingApi = {
  // 查询回单付收支信息分页
  getReceiptPendingPage: async (params: any) => {
    return await request.get({ url: `/payment/receipt-pending/page`, params })
  },

  // 查询回单付收支信息详情
  getReceiptPending: async (id: number) => {
    return await request.get({ url: `/payment/receipt-pending/get?id=` + id })
  },

  // 新增回单付收支信息
  createReceiptPending: async (data: ReceiptPendingVO) => {
    return await request.post({ url: `/payment/receipt-pending/create`, data })
  },

  // 修改回单付收支信息
  updateReceiptPending: async (data: ReceiptPendingVO) => {
    return await request.put({ url: `/payment/receipt-pending/update`, data })
  },

  // 删除回单付收支信息
  deleteReceiptPending: async (id: number) => {
    return await request.delete({ url: `/payment/receipt-pending/delete?id=` + id })
  },

  // 导出回单付收支信息 Excel
  exportReceiptPending: async (params) => {
    return await request.download({ url: `/payment/receipt-pending/export-excel`, params })
  },
  // 查询回单付列表
  getReceiptPendingList: async (params: any) => {
    return await request.get({ url: `/payment/receipt-pending/list`, params })
  },
  // 根据账单号查询回单付列表
  getReceiptPendingListByPaymentId: async (params: any) => {
    return await request.get({ url: `/payment/receipt-pending/list-by-payment-id`, params })
  },
  // 查询没有账单号的回单付列表
  getReceiptPendingListNoPaymentId: async (params: any) => {
    return await request.get({ url: `/payment/receipt-pending/list-without-payment-id`, params })
  }
}
