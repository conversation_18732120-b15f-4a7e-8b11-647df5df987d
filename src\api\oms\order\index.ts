import request from '@/config/axios'

// 订单 VO
export interface OrderVO {
  id: number // 订单id
  orderNo: string // 订单编码
  orderStatus: string // 订单状态
  contractNo: string // 合同编号
  shipperId: number // 发货人id
  consigneeId: number // 收货人id
  payeeId: number // 收款人id
  goodsId: number // 货品id
  // expenseId: number // 费用id
  pickUpType: string // 取件类型
  isDelivery: boolean//是否送货
  deliveryType: string // 送货类型
  takeDelivery: boolean //
  transportType: string // 运输类型
  waitReleaseGoods: boolean // 等通知放货
  existReceipt: boolean // 是否需要回单
  receiptType: string // 回单类型
  receiptNum: number // 回单份数
  positionId: number // 订单位置表id
  estimatedFreight: number // 预估运费
  consignerPay: number // 寄方付款
  consigneePay: number // 收方付款
  totalCost: number // 总费用
  remark: string // 备注
  deptId: number // 部门id
  userId: number // 用户id
  existRemark: boolean // 是否有备注
  existPicture: boolean // 是否上传图片
  startDeptId: number
  endDeptId: number
  destination: number
  goodsList?:[{
    goodsName: string,
    goodsPackageUnit: string,
    goodsNum: number,
    goodsVolume: number,
    goodsWeight: number,
    goodsPrice: number
  }],
  shipperName: string,
  shipperMobile: string,
  shipperAreaId: number,
  shipperDetailAddress: string,
  shipperIdCard: string,
  consigneeName: string,
  consigneeMobile:string,
  consigneeAreaId: number,
  consigneeDetailAddress: string,
  yongjinPt: string,
}

// 订单 API
export const OrderApi = {
  //获取订单号
  getOrderNo: async () => {
    return await request.get({ url: `/oms/order/get-orderNo` })
  },

  // 查询订单分页
  getOrderPage: async (params: any) => {
    return await request.get({ url: `/oms/order/page`, params })
  },

  // 查询订单详情
  getOrder: async (id: number) => {
    return await request.get({ url: `/oms/order/get?id=` + id })
  },

  // 新增订单
  createOrder: async (data: OrderVO) => {
    return await request.post({ url: `/oms/order/create`, data })
  },

  // 修改订单
  updateOrder: async (data: OrderVO) => {
    return await request.put({ url: `/oms/order/update`, data })
  },

  // 修改订单状态
  updateOrderStatus: async (data: OrderVO) => {
    return await request.put({ url: `/oms/order/update-orderStatus`, data })
  },

   // 确认订单
   confirmOrder: async (data: number[]) => {
    return await request.post({ url: `/oms/order/confirm`, data })
  },

  // 删除订单
  deleteOrder: async (id: number) => {
    return await request.delete({ url: `/oms/order/delete?id=` + id })
  },

  // 导出订单 Excel
  exportOrder: async (params) => {
    return await request.download({ url: `/oms/order/export-excel`, params })
  },

  getOrderPrintContent: async ( templateKey: string, orderNo : string) => {
    return await request.get({ url: `/oms/order/get-print-order-content`, params: { templateKey , orderNo }})
 }
}
