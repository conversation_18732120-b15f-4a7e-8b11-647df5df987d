import request from '@/config/axios'

// 车费结算 VO
export interface BalanceTruckVO {
  id: number // id
  balanceId: string // 费用ID
  balanceStatus: number // 结算状态
  balanceDeptName: string // 申请部门
  balanceDeptId: number // 申请部门ID
  balanceUser: string // 申请人
  truckAmount: number // 应付总车费
  paidAmount: number // 实际支付车费
  remainAmount: number // 扣款金额
  balanceDate: Date // 申请日期
  balancePaidDate: Date // 结算日期
  truckCode: string // 车牌号
  chargeUser: string // 收款人
  chargeUserMobile: string // 收款人手机号
  chargeUserBank: number // 收款人银行代码
  chargeUserBankAccount: string // 收款人银行
  settleUser: string // 结算人
  settleDeptId: number // 结算部门id
  settleDeptName: string // 结算部门名称
}

// 车费结算 API
export const BalanceTruckApi = {
  // 查询车费结算分页
  getBalanceTruckPage: async (params: any) => {
    return await request.get({ url: `/payment/balance-truck/page`, params })
  },

  // 查询车费结算详情
  getBalanceTruck: async (id: number) => {
    return await request.get({ url: `/payment/balance-truck/get?id=` + id })
  },

  // 新增车费结算
  createBalanceTruck: async (data: BalanceTruckVO) => {
    return await request.post({ url: `/payment/balance-truck/create`, data })
  },

  // 修改车费结算
  updateBalanceTruck: async (data: BalanceTruckVO) => {
    return await request.put({ url: `/payment/balance-truck/update`, data })
  },

  // 删除车费结算
  deleteBalanceTruck: async (id: number) => {
    return await request.delete({ url: `/payment/balance-truck/delete?id=` + id })
  },

  // 结算车费结算
  settleBalanceTruck: async (id: number) => {
    return await request.post({ url: `/payment/balance-truck/settle?id=` + id })
  },

  // 反结算车费结算
  revokeSettleBalanceTruck: async (id: number) => {
    return await request.post({ url: `/payment/balance-truck/revoke-settle?id=` + id })
  },

  // 导出车费结算 Excel
  exportBalanceTruck: async (params) => {
    return await request.download({ url: `/payment/balance-truck/export-excel`, params })
  },
  // 获取车费结算详情
  getBalanceTruckByBalanceId: async (balanceId: string) => {
    return await request.get({ url: `/payment/balance-truck/get-by-balance-id?balanceId=` + balanceId })
  },
}
