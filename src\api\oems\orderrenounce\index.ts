import request from '@/config/axios'

// 弃货 VO
export interface OrderRenounceVO {
  id: number // 编号
  renounceNo: string // 弃货编号
  waybillId: number // 运单ID
  waybillNo: string // 运单编号
  renounceStatus: number // 弃货状态
  renounceBranchId: number // 弃货到达站点ID
  renounceBranchName: string // 弃货到达站点名
  goodsName: string // 货物名称
  goodsUnit: number // 货物包装
  goodsNum: number // 货物件数
  goodsWeight: number // 货物重量
  goodsVolume: number // 货物体积
  applyBranchId: number // 申请站点ID
  applyBranchName: string // 申请站点名
  applyTime: Date // 申请时间
  applyReason: string // 申请原因
  auditBranchId: number // 审批站点ID
  auditBranchName: string // 审批站点名
  auditTime: Date // 审批退货时间
  rejectReason: string // 驳回原因
  remark: string // 备注
}

// 弃货 API
export const OrderRenounceApi = {
  // 查询弃货分页
  getOrderRenouncePage: async (params: any) => {
    return await request.get({ url: `/oems/order-renounce/page`, params })
  },

  // 查询弃货详情
  getOrderRenounce: async (id: number) => {
    return await request.get({ url: `/oems/order-renounce/get?id=` + id })
  },

  // 新增弃货
  createOrderRenounce: async (data: OrderRenounceVO) => {
    return await request.post({ url: `/oems/order-renounce/create`, data })
  },

  // 修改弃货
  updateOrderRenounce: async (data: OrderRenounceVO) => {
    return await request.put({ url: `/oems/order-renounce/update`, data })
  },

  // 删除弃货
  deleteOrderRenounce: async (id: number) => {
    return await request.delete({ url: `/oems/order-renounce/delete?id=` + id })
  },

  // 导出弃货 Excel
  exportOrderRenounce: async (params) => {
    return await request.download({ url: `/oems/order-renounce/export-excel`, params })
  },
}
