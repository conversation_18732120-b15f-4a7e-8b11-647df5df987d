import request from '@/config/axios'

// 回单付回款记录 VO
export interface MonthRecordVO {
  id: number // id
  paymentId: string // 账单ID
  paidChannel: string // 支付渠道
  receivedUser: string // 收款用户
  financeDeptId: number // 收款部门
  financeDeptName: string // 收款部门名称
  receivedAmount: number // 收款金额
  paidTime: Date // 缴款日期
  status: boolean // 收款状态 1: 正常 ；0：撤销收款
  remark: string // 备注
  revoker: string // 撤销人员
  revokeTime: Date // 撤销时间
}

// 回单付回款记录 API
export const MonthRecordApi = {
  // 查询回单付回款记录分页
  getMonthRecordPage: async (params: any) => {
    return await request.get({ url: `/payment/month-record/page`, params })
  },

  // 查询回单付回款记录详情
  getMonthRecord: async (id: number) => {
    return await request.get({ url: `/payment/month-record/get?id=` + id })
  },

  // 新增回单付回款记录
  createMonthRecord: async (data: MonthRecordVO) => {
    return await request.post({ url: `/payment/month-record/create`, data })
  },

  // 修改回单付回款记录
  updateMonthRecord: async (data: MonthRecordVO) => {
    return await request.put({ url: `/payment/month-record/update`, data })
  },

  // 删除回单付回款记录
  deleteMonthRecord: async (id: number) => {
    return await request.delete({ url: `/payment/month-record/delete?id=` + id })
  },

  // 导出回单付回款记录 Excel
  exportMonthRecord: async (params) => {
    return await request.download({ url: `/payment/month-record/export-excel`, params })
  },
  /**
   * 获取收款记录
   * @param paymentId
   */
  getMonthRecordByPaymentId(paymentId) {
    return request.get({ url: `/payment/month/getMonthRecord?paymentId=${paymentId}` })
  },
  /**
   * 撤销收款
   * @param param
   */
  revokePayment(param: { paymentId: any; remark: string; id: any }) {
    return request.delete({ url: `/payment/month/revokePayment`, data: param })
  }
}
