import request from '@/config/axios'

// 路由 VO
export interface RoutePublicVO {
  id: number // 编号
  branchRouteName: string // 路由名称
  routeType: number // 路由类型（13601市内13602省内13603省际）
  routeMode: number // 路由模式（13301自营13302承包13303合作）
  loadDeptId: number // 始发部门id
  loadDeptName: string // 始发部门名称
  discDeptId: number // 到达部门id
  discDeptName: string // 到达部门名称
  operateRate: number // 操作费比例
  isSort: number // 是否开启分拣费
  active: number // 启用状态（0正常 1停用）
  remark: string // 备注
}

// 路由 API
export const RoutePublicApi = {
  // 查询路由分页
  getRoutePage: async (params: any) => {
    return await request.get({ url: `/system/route_public/page`, params })
  },

  // 查询路由详情
  getRoute: async (id: number) => {
    return await request.get({ url: `/system/route_public/get?id=` + id })
  },

  // 查询路由详情
  getRouteByDeptId: async (id: number) => {
    return await request.get({ url: `/system/route_public/getRouteByDeptId?id=` + id })
  },

  // 查询路由详情
  getRouteCheck: async (loadDeptId: number, discDeptId: number) => {
    return await request.get({ url: `/system/route_public/getRoute`, params: { loadDeptId, discDeptId } })
  },

  // 新增路由
  createRoute: async (data: RoutePublicVO) => {
    return await request.post({ url: `/system/route_public/create`, data })
  },

  // 修改路由
  updateRoute: async (data: RoutePublicVO) => {
    return await request.put({ url: `/system/route_public/update`, data })
  },

  // 删除路由
  deleteRoute: async (id: number) => {
    return await request.delete({ url: `/system/route_public/delete?id=` + id })
  },

  // 删除路由
  deleteRoutePublicList: async (ids: any[]) => {
    return await request.delete({ url: `/system/route_public/deleteRoutePublicList?ids=` + ids })
  },

  // 导出路由 Excel
  exportRoute: async (params) => {
    return await request.download({ url: `/system/route_public/export-excel`, params })
  },

  // ==================== 子表（路由节点） ====================

  // 获得路由节点列表
  getRouteBranchListByRouteLineId: async (routeLineId) => {
    return await request.get({ url: `/system/route_public/route-branch/list-by-route-line-id?routeLineId=` + routeLineId })
  },
  // 生成路由
  generateRoute: async (params: any) => {
    return await request.get({ url: `/system/route_public/routeGenerate`, params })
  },

  // 初始化方法
  initRouteGeneration: async () => {
    return await request.get({ url: `/system/route_public/init` })
  },

  // 获取生成路由（临时存储）
  getRouteGenerate: async () => {
    return await request.get({ url: `/system/route_public/getRouteGenerate` })
  },

  // 删除生成路由
  deleteRouteGenerateAll: async () => {
    return await request.delete({ url: `/system/route_public/deleteRouteGenerateAll` })
  },

  // 删除生成路由列表
  deleteRouteGenerateList: async (params: any) => {
    return await request.delete({ url: `/system/route_public/deleteRouteGenerateList`, params })
  },

  // 转存生成路由到路由表
  transferRoute: async (data: any[]) => {
    return await request.post({ url: `/system/route_public/transfer`, data })
  }

}
