import request from '@/config/axios'

// 订单运单货品 VO
export interface GoodsVO {
  id: number // id
  orderId: number // 订单id
  waybillId: number // 运单id
  remark: string // 备注
  goodsPrice: number // 货品价值
  goodsName: string // 货品名称
  goodsVolume: number // 货品总体积
  goodsNum: number // 货品数量
  goodsPackageUnit: string // 货品打包单位
  goodsWeight: number // 货品总重量
}

// 订单运单货品 API
export const GoodsApi = {
  // 查询订单运单货品分页
  getGoodsPage: async (params: any) => {
    return await request.get({ url: `/gms/omsgoods/page`, params })
  },

  // 查询订单运单货品详情
  getGoods: async (id: number) => {
    return await request.get({ url: `/gms/omsgoods/get?id=` + id })
  },

  // 新增订单运单货品
  createGoods: async (data: GoodsVO) => {
    return await request.post({ url: `/gms/omsgoods/create`, data })
  },

  // 修改订单运单货品
  updateGoods: async (data: GoodsVO) => {
    return await request.put({ url: `/gms/omsgoods/update`, data })
  },

  // 删除订单运单货品
  deleteGoods: async (id: number) => {
    return await request.delete({ url: `/gms/omsgoods/delete?id=` + id })
  },

  // 导出订单运单货品 Excel
  exportGoods: async (params) => {
    return await request.download({ url: `/gms/omsgoods/export-excel`, params })
  },
}
