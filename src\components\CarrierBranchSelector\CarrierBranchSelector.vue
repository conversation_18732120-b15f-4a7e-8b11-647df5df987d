<template>
  <el-form-item :label="label" :prop="propName">
    <el-select
      v-model="selectedBranchId"
      @change="handleBranchChange"
      filterable
      remote
      reserve-keyword
      :remote-method="remoteMethod"
      :placeholder="placeholder"
      class="!w-240px"
    >
      <el-option v-for="item in options" :key="item.id" :label="item.branchName" :value="item.id" />
    </el-select>
  </el-form-item>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { TransferCarrierApi } from '@/api/system/transfercarrier'

// 定义选项列表
const options = ref([])
const selectedBranchId = ref(null) // 用于存储选中的网点ID
const loading = ref(false) // 用于控制加载状态
const list = ref([]) // 用于存储从服务器获取的数据

// 定义组件接收的外部属性
const props = defineProps({
  propName: {
    type: String,
    required: true
  },
  modelValue: {
    type: [String, Number],
    default: null
  },
  label: {
    type: String,
    default: '网点选择'
  },
  placeholder: {
    type: String,
    default: ''
  },
  carrierId: {
    type: [String, Number],
    default: undefined
  }
})

const emit = defineEmits(['update:modelValue', 'branch-selected'])

// 远程搜索方法
const remoteMethod = (query) => {
  if (query) {
    loading.value = true
    setTimeout(() => {
      loading.value = false
      options.value = list.value.filter((item) => {
        return item.branchName.toLowerCase().includes(query.toLowerCase())
      })
    }, 200)
  } else {
    options.value = []
  }
}

// 在组件挂载完成后获取网点列表
onMounted(async () => {
  fetchData()
})

// 监听 carrierId 的变化
watch(() => props.carrierId, async (newVal) => {
  if (newVal !== undefined) {
    fetchData();
  }
});

// 获取数据的方法
const fetchData = async () => {
  try {
    const response = await TransferCarrierApi.getTransferBranchList(props.carrierId)
    console.log(props.carrierId);
    
    console.log('获取数据成功:', response)
    list.value = response || []
    initSelectedBranch(props.modelValue)
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败，请稍后再试！')
  }
}


// 初始化 selectedBranchId
const initSelectedBranch = (id) => {
  if (id) {
    const selectedBranch = list.value.find((item) => item.id === id)
    if (selectedBranch) {
      selectedBranchId.value = id
    }
  }
}

// 监听 modelValue 变化并更新 selectedBranchId
watch(
  () => props.modelValue,
  (newVal) => {
    selectedBranchId.value = newVal
    initSelectedBranch(newVal)
  }
)

const handleBranchChange = (value) => {
  const selectedBranch = list.value.find((item) => item.id === value)
  if (selectedBranch) {

    emit('update:modelValue', value)
    emit('branch-selected', {
      branchId: selectedBranch.id,
      branchName: selectedBranch.branchName
    })
  }
}
</script>






  