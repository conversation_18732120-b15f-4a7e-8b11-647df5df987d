import request from '@/config/axios'

// 车辆事故记录 VO
export interface AccidentVO {
  id: number // 编号
  vehicleLicenseNumber: string // 车牌号码
  driverName: string // 司机姓名
  identityCardNumber: string // 身份证号
  accidentLocation: string // 事故地点
  accidentPicture: string // 事故照片
  accidentTime: Date // 事故时间
  accidentProcess: string // 事故经过
  accidentState: string // 事故状态
  remark: string // 备注
}

// 车辆事故记录 API
export const AccidentApi = {
  // 查询车辆事故记录分页
  getAccidentPage: async (params: any) => {
    return await request.get({ url: `/vms/accident/page`, params })
  },

  // 查询车辆事故记录详情
  getAccident: async (id: number) => {
    return await request.get({ url: `/vms/accident/get?id=` + id })
  },

  // 新增车辆事故记录
  createAccident: async (data: AccidentVO) => {
    return await request.post({ url: `/vms/accident/create`, data })
  },

  // 修改车辆事故记录
  updateAccident: async (data: AccidentVO) => {
    return await request.put({ url: `/vms/accident/update`, data })
  },

  // 删除车辆事故记录
  deleteAccident: async (id: number) => {
    return await request.delete({ url: `/vms/accident/delete?id=` + id })
  },

  // 导出车辆事故记录 Excel
  exportAccident: async (params) => {
    return await request.download({ url: `/vms/accident/export-excel`, params })
  },
}
