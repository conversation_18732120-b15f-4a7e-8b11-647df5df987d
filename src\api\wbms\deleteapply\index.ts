import request from '@/config/axios'

// 运单删除申请 VO
export interface DeleteApplyVO {
  id: number // id
  waybillNo: string // 运单号
  waybillStatus: string // 运单状态
  deleteApplyStatus: number // 删除申请状态
  applyUserId: number // 申请人编号
  applyUserName: string // 申请人名称
  applyDeptId: number // 申请部门编号
  applyDeptName: string // 申请部门名称
  applyReason: string // 申请原因
  auditRemark: string // 审核备注
  auditUserId: number // 审核人编号
  auditUserName: string // 审核人名称
  auditDeptId: number // 审核部门编号
  auditDeptName: string // 审核部门名称
  auditTime: Date // 审核时间
}

// 运单删除申请 API
export const DeleteApplyApi = {
  // 查询运单删除申请分页
  getDeleteApplyPage: async (params: any) => {
    return await request.get({ url: `/wbms/delete-apply/page`, params })
  },

  // 查询运单删除申请详情
  getDeleteApply: async (id: number) => {
    return await request.get({ url: `/wbms/delete-apply/get?id=` + id })
  },

  // 新增运单删除申请
  createDeleteApply: async (data: DeleteApplyVO) => {
    return await request.post({ url: `/wbms/delete-apply/create`, data })
  },

  // 修改运单删除申请
  updateDeleteApply: async (data: DeleteApplyVO) => {
    return await request.put({ url: `/wbms/delete-apply/update`, data })
  },

  // 删除运单删除申请
  deleteDeleteApply: async (id: number) => {
    return await request.delete({ url: `/wbms/delete-apply/delete?id=` + id })
  },

  // 导出运单删除申请 Excel
  exportDeleteApply: async (params) => {
    return await request.download({ url: `/wbms/delete-apply/export-excel`, params })
  },
}
