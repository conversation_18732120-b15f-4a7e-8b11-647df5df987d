import request from '@/config/axios'

// 库区 VO
export interface GodownVO {
  id: number // 编号
  godownName: string // 库区名称
  godownNo: string // 库区编号
  branchId: number // 所属站点
  remark: string // 备注
}

// 库区 API
export const GodownApi = {
  // 查询库区分页
  getGodownPage: async (params: any) => {
    return await request.get({ url: `/system/godown/page`, params })
  },

  // 查询库区详情
  getGodown: async (id: number) => {
    return await request.get({ url: `/system/godown/get?id=` + id })
  },

  // 新增库区
  createGodown: async (data: GodownVO) => {
    return await request.post({ url: `/system/godown/create`, data })
  },

  // 修改库区
  updateGodown: async (data: GodownVO) => {
    return await request.put({ url: `/system/godown/update`, data })
  },

  // 删除库区
  deleteGodown: async (id: number) => {
    return await request.delete({ url: `/system/godown/delete?id=` + id })
  },

  // 导出库区 Excel
  exportGodown: async (params) => {
    return await request.download({ url: `/system/godown/export-excel`, params })
  },

// ==================== 子表（库区分组） ====================

  // 获得库区分组分页
  getGodownGroupPage: async (params) => {
    return await request.get({ url: `/system/godown/godown-group/page`, params })
  },
  // 新增库区分组
  createGodownGroup: async (data) => {
    return await request.post({ url: `/system/godown/godown-group/create`, data })
  },

  // 修改库区分组
  updateGodownGroup: async (data) => {
    return await request.put({ url: `/system/godown/godown-group/update`, data })
  },

  // 删除库区分组
  deleteGodownGroup: async (id: number) => {
    return await request.delete({ url: `/system/godown/godown-group/delete?id=` + id })
  },

  // 获得库区分组
  getGodownGroup: async (id: number) => {
    return await request.get({ url: `/system/godown/godown-group/get?id=` + id })
  },
}
