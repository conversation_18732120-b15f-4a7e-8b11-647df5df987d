import request from '@/config/axios'

// 运单路由信息 VO
export interface EventRouteVO {
  id: number // id
  deptId: number // 操作部门id
  deptName: string // 操作部门名称
  eventNo: string // 操作运单编号
  eventLog: string // 操作内容
  eventNode: string // 操作节点
  eventNodeValue: string // 操作节点值
  eventType: string // 操作类型
  userCode: number // 操作员ID
  userName: string // 操作员名称
}

// 运单路由信息 API
export const EventRouteApi = {
  // 查询运单路由信息分页
  getEventRoutePage: async (params: any) => {
    return await request.get({ url: `/wbms/event-route/page`, params })
  },

  // 查询运单路由信息详情
  getEventRoute: async (id: number) => {
    return await request.get({ url: `/wbms/event-route/get?id=` + id })
  },

  // 新增运单路由信息
  createEventRoute: async (data: EventRouteVO) => {
    return await request.post({ url: `/wbms/event-route/create`, data })
  },

  // 修改运单路由信息
  updateEventRoute: async (data: EventRouteVO) => {
    return await request.put({ url: `/wbms/event-route/update`, data })
  },

  // 删除运单路由信息
  deleteEventRoute: async (data: EventRouteVO) => {
    return await request.post({ url: `/wbms/event-route/delete`, data })
  },

  // 导出运单路由信息 Excel
  exportEventRoute: async (params) => {
    return await request.download({ url: `/wbms/event-route/export-excel`, params })
  },
}
