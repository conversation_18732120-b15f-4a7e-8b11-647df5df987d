import request from '@/config/axios'

// 账户流水 VO
export interface AccountChangeLogVO {
  id: number // 自增序列
  accountId: number // 账户id
  relyAccountId: number // 关联账户id
  changeSource: string // 变更来源
  changeAmount: number // 变更金额
  amountBefore: number // 变更前金额
  amountAfter: number // 变更后金额
  changeTime: Date // 变更时间
  userId: number // 操作人id
  businessId: number // 业务id（订单id、充值申请id、提现申请id）
}

/**
 * 变更来源（1-开单扣款 2-分成结算 3-划转 4-充值 5-提现 6-调账 7-删单退款 8-改单退款 9-改单重新扣款）
 */
export const ACCOUNT_CHANGE_SOURCE_LIST = reactive([
  { label: '开单扣款', value: '1' },
  { label: '分成结算', value: '2' },
  { label: '划转', value: '3' },
  { label: '充值', value: '4' },
  { label: '提现', value: '5' },
  { label: '调账', value: '6' },
  { label: '删单退款', value: '7' },
  { label: '改单退款', value: '8' },
  { label: '改单重新扣款', value: '9' },
])

export const getAccountChangeSourceLabel = (type: string) => {
  if (!type) {
    return ''
  }
  return ACCOUNT_CHANGE_SOURCE_LIST.find(item => item.value === type)?.label
}

// 账户流水 API
export const AccountChangeLogApi = {
  // 查询账户流水分页
  getAccountChangeLogPage: async (params: any) => {
    return await request.get({ url: `/wljh/account-change-log/page`, params })
  },

  // 查询账户流水详情
  getAccountChangeLog: async (id: number) => {
    return await request.get({ url: `/wljh/account-change-log/get?id=` + id })
  },

  // 新增账户流水
  createAccountChangeLog: async (data: AccountChangeLogVO) => {
    return await request.post({ url: `/wljh/account-change-log/create`, data })
  },

  // 修改账户流水
  updateAccountChangeLog: async (data: AccountChangeLogVO) => {
    return await request.put({ url: `/wljh/account-change-log/update`, data })
  },

  // 删除账户流水
  deleteAccountChangeLog: async (id: number) => {
    return await request.delete({ url: `/wljh/account-change-log/delete?id=` + id })
  },

  // 导出账户流水 Excel
  exportAccountChangeLog: async (params) => {
    return await request.download({ url: `/wljh/account-change-log/export-excel`, params })
  }
}
