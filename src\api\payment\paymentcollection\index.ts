import request from '@/config/axios'

// 代收款管理 VO
export interface CollectionVO {
  id: number // id
  paymentId: string // 费用ID
  chargeRefId: number // 运单id
  chargeRefNo: string // 运单号
  receiptDate: Date // 提货日期
  amountCollection: number // 代收款金额
  amountCollectionCommission: number // 代收款手续费
  totalAmountKf: number // 扣付
  amountCollectionActual: number // 实际发放代收款
  balanceDeptName: string // 申请部门
  billDeptId: number // 起始部门ID
  billDeptName: string // 起始部门
  discDeptId: number // 到达部门ID
  discDeptName: string // 到达部门
  paymentStatus: number // 支付状态
  paymentDeptId: number // 支付部门ID
  paymentDeptName: string // 支付部门名称
  paymentUser: string // 支付用户
  paymentDate: Date // 支付日期
  contractUser: string // 会员
  contractUserMobile: string // 收款人手机号
  contractUserBank: number // 收款人银行
  contractUserBankAccount: string // 收款人银行卡号
  auditUser: string // 审核人
  auditDeptId: number // 审核部门id
  auditDeptName: string // 审核部门名称
  auditStatus: number // 审核状态
}

// 代收款管理 API
export const CollectionApi = {
  // 查询代收款管理分页
  getCollectionPage: async (params: any) => {
    return await request.get({ url: `/payment/collection/page`, params })
  },

  // 查询代收款管理详情
  getCollection: async (id: number) => {
    return await request.get({ url: `/payment/collection/get?id=` + id })
  },

  // 新增代收款管理
  createCollection: async (data: CollectionVO) => {
    return await request.post({ url: `/payment/collection/create`, data })
  },

  // 修改代收款管理
  updateCollection: async (data: CollectionVO) => {
    return await request.put({ url: `/payment/collection/update`, data })
  },

  // 删除代收款管理
  deleteCollection: async (id: number) => {
    return await request.delete({ url: `/payment/collection/delete?id=` + id })
  },

  // 导出代收款管理 Excel
  exportCollection: async (params) => {
    return await request.download({ url: `/payment/collection/export-excel`, params })
  },
  // 审核
  audit: async (id: number) => {
    return await request.post({ url: `/payment/collection/audit`, data: id })
  }
}
