import request from '@/config/axios'

// 货品信息 VO
export interface GoodsVO {
  id: number // 编号
  goodsCode: string // 货品编码
  goodsName: string // 货品名称
  goodsType: number // 货品类别
  goodsUnit: string // 货品单位
  defaultVolume: string // 默认体积
  defaultWeight: string // 默认重量
  goodsColor: string // 货品颜色
  goodShelfLife: string // 货品保质期
  goodsBrand: string // 货品品牌
  goodsProducer: string // 货品产地
  goodsExtra: string // 其他属性
  remark: string // 备注
}

// 货品信息 API
export const GoodsApi = {
  // 查询货品信息分页
  getGoodsPage: async (params: any) => {
    return await request.get({ url: `/gms/goods/page`, params })
  },

  // 查询货品信息详情
  getGoods: async (id: number) => {
    return await request.get({ url: `/gms/goods/get?id=` + id })
  },

  getGoodsAndTypeByGoodName: async (name: string) => {
    return await request.get({ url: `/gms/goods/list?name=` + name })
  },

  // 新增货品信息
  createGoods: async (data: GoodsVO) => {
    return await request.post({ url: `/gms/goods/create`, data })
  },

  // 修改货品信息
  updateGoods: async (data: GoodsVO) => {
    return await request.put({ url: `/gms/goods/update`, data })
  },

  // 删除货品信息
  deleteGoods: async (id: number) => {
    return await request.delete({ url: `/gms/goods/delete?id=` + id })
  },

  // 导出货品信息 Excel
  exportGoods: async (params) => {
    return await request.download({ url: `/gms/goods/export-excel`, params })
  },

// ==================== 子表（货物物流信息） ====================

  // 获得货物物流信息分页
  getGoodLogisticsPage: async (params) => {
    return await request.get({ url: `/gms/goods/good-logistics/page`, params })
  },
  // 新增货物物流信息
  createGoodLogistics: async (data) => {
    return await request.post({ url: `/gms/goods/good-logistics/create`, data })
  },

  // 修改货物物流信息
  updateGoodLogistics: async (data) => {
    return await request.put({ url: `/gms/goods/good-logistics/update`, data })
  },

  // 删除货物物流信息
  deleteGoodLogistics: async (id: number) => {
    return await request.delete({ url: `/gms/goods/good-logistics/delete?id=` + id })
  },

  // 获得货物物流信息
  getGoodLogistics: async (id: number) => {
    return await request.get({ url: `/gms/goods/good-logistics/get?id=` + id })
  },
}
