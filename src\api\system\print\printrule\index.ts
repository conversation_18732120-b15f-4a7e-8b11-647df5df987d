import request from '@/config/axios'

// 打印规则配置 VO
export interface PrintRuleVO {
  id: number // 自增序列
  templateName: string // 业务模块名称
  templateKey: string // 业务模块唯一标识
  printNum: number // 打印数量上限 默认 1
  isEnable: string // 是否启用：0 否 1 是
  explainVal: string // 说明
  type: string // 1：标题 2：业务
  status: string // 有效标志1：有效；0：无效
  pid: number // 父级ID
  labelName: string // 标签名字
}

// 打印规则配置 API
export const PrintRuleApi = {
  // 查询打印规则配置分页
  getPrintRulePage: async (params: any) => {
    return await request.get({ url: `/wljh/print-rule/page`, params })
  },

  // 查询打印规则配置详情
  getPrintRule: async (id: number) => {
    return await request.get({ url: `/wljh/print-rule/get?id=` + id })
  },

  // 新增打印规则配置
  createPrintRule: async (data: PrintRuleVO) => {
    return await request.post({ url: `/wljh/print-rule/create`, data })
  },

  // 修改打印规则配置
  updatePrintRule: async (data: PrintRuleVO) => {
    return await request.put({ url: `/wljh/print-rule/update`, data })
  },

  // 删除打印规则配置
  deletePrintRule: async (id: number) => {
    return await request.delete({ url: `/wljh/print-rule/delete?id=` + id })
  },

  // 导出打印规则配置 Excel
  exportPrintRule: async (params) => {
    return await request.download({ url: `/wljh/print-rule/export-excel`, params })
  },

  // 获得所有打印规则配置
  getPrintRuleAll: async (params: any) => {
    return await request.get({ url: `/wljh/print-rule/getAllInfo`, params })
  },

  // 获取当前用户的打印模板以及系统模板
  getPrintTemplateAll: async (params: any) => {
    return await request.get({ url: `/wljh/print-rule/getPrintTemplateAll`, params })
  },

  // 添加打印规则配置及模板信息
  addPrintRule: async (data: any) => {
    return await request.post({ url: `/wljh/print-rule/add`, data })
  }
}
