import request from '@/config/axios'

// 网点划转记录 VO
export interface NetworkTransferLogVO {
  id: number // 自增序列
  logisticsNetworkId: number // 网点id
  networkFreightPreValue: number // 网点运费预付账户增加金额
  networkFreightDivideValue: number // 网点运费分成账户增加金额
  networkTaxValue: number // 网点税费账户增加金额
  networkBrokerageValue: number // 网点佣金账户增加金额
  platformFreightValue: number // 平台运费账户增加金额
  platformProfitValue: number // 平台毛利账户增加金额
  platformBrokerageValue: number // 平台佣金账户增加金额
  waybillCodeSet: string // 运单号集合
  transferTime: Date // 划转时间
  transferUserId: number // 划转人id
}

// 网点划转记录 API
export const NetworkTransferLogApi = {
  // 查询网点划转记录分页
  getNetworkTransferLogPage: async (params: any) => {
    return await request.get({ url: `/wljh/network-transfer-log/page`, params })
  },

  // 查询网点划转记录详情
  getNetworkTransferLog: async (id: number) => {
    return await request.get({ url: `/wljh/network-transfer-log/get?id=` + id })
  },

  // 新增网点划转记录
  createNetworkTransferLog: async (data: NetworkTransferLogVO) => {
    return await request.post({ url: `/wljh/network-transfer-log/create`, data })
  },

  // 修改网点划转记录
  updateNetworkTransferLog: async (data: NetworkTransferLogVO) => {
    return await request.put({ url: `/wljh/network-transfer-log/update`, data })
  },

  // 删除网点划转记录
  deleteNetworkTransferLog: async (id: number) => {
    return await request.delete({ url: `/wljh/network-transfer-log/delete?id=` + id })
  },

  // 导出网点划转记录 Excel
  exportNetworkTransferLog: async (params) => {
    return await request.download({ url: `/wljh/network-transfer-log/export-excel`, params })
  }
}