import request from '@/config/axios'

// 提现申请 VO
export interface TakeCashApplyVO {
  id: number // 自增序列
  accountRole: string // 账户角色（1- 平台 2-网点 3-物流公司 ）
  mainId: number // 网点id/物流公司id
  takeAmount: number // 提现金额
  certUrl: string // 支付凭证
  applyUserId: number // 申请人id
  applyTime: Date // 申请时间
  applyStatus: string // 提现申请状态（1-待审核 2-审核通过 3-审核不通过 4-平台已转账 5-确认已收款）
  auditUserId: number // 审核人id
  auditTime: Date, // 审核时间
  auditRemark: string // 审核备注
}

/**
 * 提现申请状态（1-待审核 2-审核通过 3-审核不通过 4-平台已转账 5-确认已收款）
 */
export const TAKE_CASH_APPLY_STATUS_LIST = reactive([
  { label: '待审核', value: '1' },
  { label: '审核通过', value: '2' },
  { label: '审核不通过', value: '3' },
  { label: '平台已转账', value: '4' },
  { label: '确认已收款', value: '5' }
])
export const getTakeCashApplyStatusLabel = (type: string) => {
  if (!type) {
    return ''
  }
  return TAKE_CASH_APPLY_STATUS_LIST.find(item => item.value === type)?.label
}

// 提现申请 API
export const TakeCashApplyApi = {
  // 查询提现申请分页
  getTakeCashApplyPage: async (params: any) => {
    return await request.get({ url: `/wljh/take-cash-apply/page`, params })
  },

  // 查询提现申请详情
  getTakeCashApply: async (id: number) => {
    return await request.get({ url: `/wljh/take-cash-apply/get?id=` + id })
  },

  // 新增提现申请
  createTakeCashApply: async (data: TakeCashApplyVO) => {
    return await request.post({ url: `/wljh/take-cash-apply/create`, data })
  },

  // 修改提现申请
  updateTakeCashApply: async (data: any) => {
    return await request.put({ url: `/wljh/take-cash-apply/update`, data })
  },

  // 删除提现申请
  deleteTakeCashApply: async (id: number) => {
    return await request.delete({ url: `/wljh/take-cash-apply/delete?id=` + id })
  },

  // 导出提现申请 Excel
  exportTakeCashApply: async (params) => {
    return await request.download({ url: `/wljh/take-cash-apply/export-excel`, params })
  },

  // 获取申请部门下拉列表
  getMainSelectList: async (params) => {
    return await request.get({ url: `/wljh/take-cash-apply/getMainSelectList`, params })
  },

  // 审核提现申请
  auditTakeCashApply: async (data: any) => {
    return await request.post({ url: `/wljh/take-cash-apply/audit`, data })
  },
  // 提现确认前校验
  confirmTakeCashCheck: async (data: any) => {
    return await request.post({ url: `/wljh/take-cash-apply/confirmCheck`, data })
  },
  // 提现确认
  confirmTakeCashApply: async (data: any) => {
    return await request.post({ url: `/wljh/take-cash-apply/confirm`, data })
  },
  // 收款确认
  confirmTakeCashCollect: async (data: any) => {
    return await request.post({ url: `/wljh/take-cash-apply/confirmCollect`, data })
  },
}
