import { WaybillVO } from '@/api/wbms/waybill'
import request from '@/config/axios'

// 派车送货 VO
export interface DeliveryVoyageVO {
  id: number // 编号
  transportNo: string // 接送批次号
  voyageType: number // 批次类型
  voyageStatus: number // 批次状态
  branchId: number // 部门ID
  branchName: string // 部门名称
  vehicleId: number // 车辆ID
  vehicleLicenseNumber: string // 车牌号
  vehicleType: number // 车辆类型
  driverId: number // 驾驶员ID
  driverName: string // 驾驶员姓名
  contactMobile: string // 驾驶员手机号
  courierId: number // 派送员ID
  courierName: string // 派送员姓名
  courierMobile: string // 派送员手机号
  remark: string // 备注
  amountDelivery: number //派送费
}

// 派车送货 API
export const DeliveryVoyageApi = {
  // 查询派车送货分页
  getDeliveryVoyagePage: async (params: any) => {
    return await request.get({ url: `/exp/delivery-voyage/page`, params })
  },

  // 查询派车送货分页(按单)
  getVoyageWaybillPage: async (params: any) => {
    return await request.get({ url: `/exp/delivery-voyage/page-waybill`, params })
  },

  // 查询派车送货详情
  getDeliveryVoyage: async (id: number) => {
    return await request.get({ url: `/exp/delivery-voyage/get?id=` + id })
  },

  // 查询派车送货详情(按批次号)
  getVoyageByTransportNo: async (transportNo: string) => {
    return await request.get({ url: `/exp/delivery-voyage/get-transportNo?transportNo=` + transportNo })
  },

  // 新增派车送货
  createDeliveryVoyage: async (data: DeliveryVoyageVO) => {
    return await request.post({ url: `/exp/delivery-voyage/create`, data })
  },

  // 修改派车送货
  updateDeliveryVoyage: async (data: DeliveryVoyageVO) => {
    return await request.put({ url: `/exp/delivery-voyage/update`, data })
  },

  // 修改派车送货状态
  updateVoyageStatus: async (data: DeliveryVoyageVO) => {
    return await request.put({ url: `/exp/delivery-voyage/update-voyageStatus`, data })
  },

  // 删除派车送货
  deleteDeliveryVoyage: async (id: number) => {
    return await request.delete({ url: `/exp/delivery-voyage/delete?id=` + id })
  },

  // 导出派车送货 Excel
  exportDeliveryVoyage: async (params) => {
    return await request.download({ url: `/exp/delivery-voyage/export-excel`, params })
  },

// ==================== 子表（收派订单） ====================

  // 获得收派订单
  getDeliveryVoyageOrderByTransportNo: async (transportNo) => {
    return await request.get({ url: `/exp/delivery-voyage/voyage-order/get-by-transportNo?transportNo=` + transportNo })
  },

  createVoyageOrder :async (waybillList: WaybillVO,expVoyageRespVO: DeliveryVoyageVO) => {
    return await request.post({ url: `/exp/delivery-voyage/voyage-order/create`,  data:{ waybillList , expVoyageRespVO}});
  },

  updateVoyageOrder: async (expVoyageOrderDOS:[]) => {
    return await request.post({ url: `/exp/delivery-voyage/voyage-order/update-status`, data:{expVoyageOrderDOS} })
  },
}
