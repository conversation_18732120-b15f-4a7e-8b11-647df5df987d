<template>
  <el-form-item :label="label" :prop="propName">
    <el-select
      v-model="selectedDestId"
      @change="handleDestChange"
      filterable
      remote
      reserve-keyword
      :remote-method="remoteMethod"
      :placeholder="placeholder"
      :class="selectClass"
      :disabled="selectDisabled"
      :automatic-dropdown="true"
    >
      <!-- @change="handleBranchChange" -->
      <el-option v-for="item in options" :key="item.id" :label="item.destName" :value="item.destId">
        <span style="float: left">{{ item.destName }}</span>
        <span style="float: right; color: var(--el-text-color-secondary); font-size: 13px">
          从{{ item.deptName }}转
        </span>
      </el-option>
    </el-select>
  </el-form-item>
</template>
<script setup>
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { OutboundApi } from '@/api/system/outbound'

// 定义选项列表
const options = ref([])
const selectedDestId = ref(null) // 用于存储选中的网点ID
const loading = ref(false) // 用于控制加载状态
const list = ref([]) // 用于存储从服务器获取的数据

// 定义组件接收的外部属性
const props = defineProps({
  propName: {
    type: String,
    required: true
  },
  modelValue: {
    type: [String, Number],
    default: null
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  selectClass: {
    type: String,
    default: '!w-240px'
  },
  selectDisabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'destination-selected'])

// 远程搜索方法
const remoteMethod = (query) => {
  if (query) {
    loading.value = true
    setTimeout(() => {
      loading.value = false
      options.value = list.value.filter((item) => {
        return item.destName.toLowerCase().includes(query.toLowerCase())
      })
    }, 200)
  } else {
    options.value = []
  }
}

// 在组件挂载完成后获取网点列表
onMounted(async () => {
  try {
    const response = await OutboundApi.getOutboundDeptList()

    list.value = response || []
    options.value = list.value

    // 初始化时设置 selectedDestId
    if (props.modelValue) {
      selectedDestId.value = props.modelValue
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败，请稍后再试！')
  }
})

//监听 modelValue 变化并更新 selectedDestId
watch(
  () => props.modelValue,
  (newVal) => {
    selectedDestId.value = newVal
  }
)

const handleDestChange = (value) => {
  const selectedDest = list.value.find((item) => item.destId === value)
  if (selectedDest) {
    emit('update:modelValue', value)
    emit('destination-selected', {
      destId: selectedDest.destId,
      destName: selectedDest.destName,
      deptId: selectedDest.deptId,
      deptName: selectedDest.deptName
    })
  }
}
</script>
<style scoped>
/* 移除原有边框和阴影，添加下划线 */
:deep(.el-select__wrapper) {
  box-shadow:  0 0 0 0  !important;
  border-radius: 0 !important;
  border-bottom: 1px solid #dcdfe6 !important;
  padding: 0 !important;
  background-color: transparent !important;
}

</style>
