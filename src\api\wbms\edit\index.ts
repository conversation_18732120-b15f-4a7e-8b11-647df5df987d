import request from '@/config/axios'

// 订单修改 VO
export interface EditVO {
  id: number // id
  waybillId: number // 运单ID
  waybillNo: string // 运单号
  waybillStatus: string // 运单状态
  currentDeptId: number // 运单当前部门ID
  currentDeptName: string // 运单当前部门名
  editStatus: number // 改单状态
  editType: number // 改单类型
  applyUserId: number // 申请人编号
  applyUserName: string // 申请人名称
  applyDeptId: number // 申请部门编号
  applyDeptName: string // 申请部门名称
  applyReason: string // 申请原因
  applyPictureUrl: string // 申请图片
  auditRemark: string // 审批备注
  auditUserId: number // 审批人编号
  auditUserName: string // 审批人名称
  auditDeptId: number // 审批部门编号
  auditDeptName: string // 审批部门名称
  auditTime: Date // 审批时间
}

// 订单修改 API
export const EditApi = {
  // 查询订单修改分页
  getEditPage: async (params: any) => {
    return await request.get({ url: `/wbms/edit/page`, params })
  },

  // 查询订单修改详情
  getEdit: async (id: number) => {
    return await request.get({ url: `/wbms/edit/get?id=` + id })
  },

  // 根据运单编号查询订单修改详情
  getEditByWaybillNo: async (waybillNo: String) => {
    return await request.get({ url: `/wbms/edit/get-by-waybill-no?waybillNo=` + waybillNo })
  },

  // 新增订单修改
  createEdit: async (data: EditVO) => {
    return await request.post({ url: `/wbms/edit/create`, data })
  },

  // 修改修改申请
  updateEdit: async (data: EditVO) => {
    return await request.put({ url: `/wbms/edit/update`, data })
  },

  // 删除订单修改
  deleteEdit: async (id: number) => {
    return await request.delete({ url: `/wbms/edit/delete?id=` + id })
  },

  // 导出订单修改 Excel
  exportEdit: async (params) => {
    return await request.download({ url: `/wbms/edit/export-excel`, params })
  },

// ==================== 子表（修改信息详细） ====================

  // 获得修改信息详细列表
  getEditDetailsListByEditId: async (editId) => {
    return await request.get({ url: `/wbms/edit/edit-details/list-by-edit-id?editId=` + editId })
  },

  //根据运单号获取修改信息列表
  getEditDetailListByWaybillNo: async (waybillNo) => {
    return await request.get({ url: `/wbms/edit/list-by-waybill-no?waybillNo=` + waybillNo })
  },
}
