import request from '@/config/axios'

// 司机 VO
export interface DriverVO {
  id: number // 司机编号
  driverName: string // 司机姓名
  contactMobile: string // 联系电话
  identityCardNumber: string // 身份证号
  driverCompany: string // 所属单位/个人
  driverAddress: string // 地址
  driverMark: string // 司机标记
  bankName: string // 银行名称
  bankAccount: string // 银行账号
  urgentContacts: string // 紧急联系人
  urgentMobile: string // 紧急联系人电话
  employmentStatus: number // 在职状态
  authenticaStatus: number // 认证状态
  remark: string // 备注
}

// 司机 API
export const DriverApi = {
  // 查询司机分页
  getDriverPage: async (params: any) => {
    return await request.get({ url: `/vms/driver/page`, params })
  },

  // 查询司机详情
  getDriver: async (id: number) => {
    return await request.get({ url: `/vms/driver/get?id=` + id })
  },

  //
  getDriverList: async () => {
    return await request.get({ url: `/vms/driver/get-list` })
  },

  // 新增司机
  createDriver: async (data: DriverVO) => {
    return await request.post({ url: `/vms/driver/create`, data })
  },

  // 修改司机
  updateDriver: async (data: DriverVO) => {
    return await request.put({ url: `/vms/driver/update`, data })
  },

  // 删除司机
  deleteDriver: async (id: number) => {
    return await request.delete({ url: `/vms/driver/delete?id=` + id })
  },

  // 导出司机 Excel
  exportDriver: async (params) => {
    return await request.download({ url: `/vms/driver/export-excel`, params })
  },

// ==================== 子表（司机证件） ====================

  // 获得司机证件分页
  getDriverCredentialPage: async (params) => {
    return await request.get({ url: `/vms/driver/driver-credential/page`, params })
  },
  // 新增司机证件
  createDriverCredential: async (data) => {
    return await request.post({ url: `/vms/driver/driver-credential/create`, data })
  },

  // 修改司机证件
  updateDriverCredential: async (data) => {
    return await request.put({ url: `/vms/driver/driver-credential/update`, data })
  },

  // 删除司机证件
  deleteDriverCredential: async (id: number) => {
    return await request.delete({ url: `/vms/driver/driver-credential/delete?id=` + id })
  },

  // 获得司机证件
  getDriverCredential: async (id: number) => {
    return await request.get({ url: `/vms/driver/driver-credential/get?id=` + id })
  },
}
