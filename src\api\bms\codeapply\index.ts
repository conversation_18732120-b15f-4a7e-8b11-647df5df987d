import request from '@/config/axios'

// 条码申请 VO
export interface CodeApplyVO {
  id: number // id
  applyNum: number // 申请数量
  isDisable: boolean // 是否禁用
  applyName: string // 申请名称
  codeRuleId: number // 申请规则id
  remark: string // 备注
  isGenerateNow: boolean // 是否立即生成
  appleDescription: string // 申请描述
  generateCodeNum: number // 生成code数量
}

// 条码申请 API
export const CodeApplyApi = {
  // 查询条码申请分页
  getCodeApplyPage: async (params: any) => {
    return await request.get({ url: `/bms/code-apply/page`, params })
  },

  // 查询条码申请详情
  getCodeApply: async (id: number) => {
    return await request.get({ url: `/bms/code-apply/get?id=` + id })
  },

  retryApplyCode: async (id: number) => {
    return await request.get({ url: `/bms/code-apply/retry/` + id })
  },


  // 新增条码申请
  createCodeApply: async (data: CodeApplyVO) => {
    return await request.post({ url: `/bms/code-apply/create`, data })
  },

  // 修改条码申请
  updateCodeApply: async (data: CodeApplyVO) => {
    return await request.put({ url: `/bms/code-apply/update`, data })
  },

  // 删除条码申请
  deleteCodeApply: async (id: number) => {
    return await request.delete({ url: `/bms/code-apply/delete?id=` + id })
  },

  // 导出条码申请 Excel
  exportCodeApply: async (params) => {
    return await request.download({ url: `/bms/code-apply/export-excel`, params })
  },
}
