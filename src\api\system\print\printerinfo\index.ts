import request from '@/config/axios'

// 打印机信息 VO
export interface PrinterInfoVO {
  id: number // 自增序列
  brandName: string // 品牌名
  parentId: number // 父级ID
  brandCode: string // 品牌型号
  instructCode: string // 指令类型：CPCL，ESC
  bluetoothName: string // 蓝牙名称
  wifiName: string // 无线设备名称
  chunkSize: number // 蓝牙传输块大小，默认20
  isEnabled: string // 是否启用：0 否 1 是
}

// 打印机信息 API
export const PrinterInfoApi = {
  // 查询打印机信息列表
  getPrinterInfoList: async (params) => {
    return await request.get({ url: `/wljh/printer-info/list`, params })
  },

  // 查询打印机信息详情
  getPrinterInfo: async (id: number) => {
    return await request.get({ url: `/wljh/printer-info/get?id=` + id })
  },

  // 新增打印机信息
  createPrinterInfo: async (data: PrinterInfoVO) => {
    return await request.post({ url: `/wljh/printer-info/create`, data })
  },

  // 修改打印机信息
  updatePrinterInfo: async (data: PrinterInfoVO) => {
    return await request.put({ url: `/wljh/printer-info/update`, data })
  },

  // 删除打印机信息
  deletePrinterInfo: async (id: number) => {
    return await request.delete({ url: `/wljh/printer-info/delete?id=` + id })
  },

  // 导出打印机信息 Excel
  exportPrinterInfo: async (params) => {
    return await request.download({ url: `/wljh/printer-info/export-excel`, params })
  }
}