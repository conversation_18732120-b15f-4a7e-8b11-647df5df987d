import request from '@/config/axios'

// 代收款转账批次 VO
export interface CollectionTransferBatchesVO {
  id: number // id
  batchNo: string // 转账批次号
  transferCreator: string // 账单创建人
  payemntAmount: number // 转账金额
  payemntAmountCommission: number // 转账手续费
  totalAmountKf: number // 扣付
  totalWaybillNum: number // 运单数量
  paymentStatus: number // 支付状态
  paymentDeptId: number // 支付部门ID
  paymentDeptName: string // 支付部门名称
  paymentUser: string // 支付用户
  paymentDate: Date // 支付日期
  auditUser: string // 审核人
  auditDeptId: number // 审核部门id
  auditDeptName: string // 审核部门名称
  auditStatus: number // 审核状态
}

// 代收款转账批次 API
export const CollectionTransferBatchesApi = {
  // 查询代收款转账批次分页
  getCollectionTransferBatchesPage: async (params: any) => {
    return await request.get({ url: `/payment/collection-transfer-batches/page`, params })
  },

  // 查询代收款转账批次详情
  getCollectionTransferBatches: async (id: number) => {
    return await request.get({ url: `/payment/collection-transfer-batches/get?id=` + id })
  },

  // 新增代收款转账批次
  createCollectionTransferBatches: async (data: CollectionTransferBatchesVO) => {
    return await request.post({ url: `/payment/collection-transfer-batches/create`, data })
  },

  // 修改代收款转账批次
  updateCollectionTransferBatches: async (data: any) => {
    return await request.put({ url: `/payment/collection-transfer-batches/update`, data })
  },
  // 修改代收款和转账批次关联关系
  updateCollectionTransferBatchDetail: async (data: any) => {
    return await request.post({ url: `/payment/collection-transfer-batches/update-detail`, data })
  },

  // 删除代收款转账批次
  deleteCollectionTransferBatches: async (ids: number[]) => {
    return await request.post({ url: `/payment/collection-transfer-batches/delete`, data: ids })
  },

  // 导出代收款转账批次 Excel
  exportCollectionTransferBatches: async (params) => {
    return await request.download({ url: `/payment/collection-transfer-batches/export-excel`, params })
  },

  // 支付代收款转账批次
  payCollectionTransferBatches: async (ids: number[]) => {
    return await request.post({ url: `/payment/collection-transfer-batches/pay`, data: ids })
  },
  // 根据转账批次号获取所有代收款信息
  getCollectionByBatchNo: async (params: any) => {
    return await request.get({ url: `/payment/collection-transfer-batches/get-collection-by-batch-no`, params })
  }
}
