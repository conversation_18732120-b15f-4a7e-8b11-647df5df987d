import request from '@/config/axios'

// 外转维护 VO
export interface OutboundVO {
  id: number // 编号
  destName: string // 目的地
  destQrychar: string // 快捷查询码
  amountTransferRatio: number // 中转费比例
  destArea: string // 地区选择
  legalPerson: string // 联系人
  legalPersonAddr: string // 联系地址
  legalPersonTel: string // 联系电话
  legalPersonMobile: string // 联系手机
  xLong: string // 经度
  yLati: string // 纬度
  destActive: number // 是否启用
  isDelivery: number // 是否允许送货
  isFree: number // 是否通知放货
  isAllowMouth: number // 是否允许到月结
  isAllowHdf: number // 是否允许回单付
  isAllowDiscTf: number // 是否允许到提付
  isAllowDiscDs: number // 是否允许到代收
  remark: string // 备注
}

// 外转维护 API
export const OutboundApi = {
  // 查询外转维护分页
  getOutboundPage: async (params: any) => {
    return await request.get({ url: `/system/outbound/page`, params })
  },

  // 查询外转维护详情
  getOutbound: async (id: number) => {
    return await request.get({ url: `/system/outbound/get?id=` + id })
  },

  // 新增外转维护
  createOutbound: async (data: OutboundVO) => {
    return await request.post({ url: `/system/outbound/create`, data })
  },

  // 修改外转维护
  updateOutbound: async (data: OutboundVO) => {
    return await request.put({ url: `/system/outbound/update`, data })
  },

  // 删除外转维护
  deleteOutbound: async (id: number) => {
    return await request.delete({ url: `/system/outbound/delete?id=` + id })
  },

  // 导出外转维护 Excel
  exportOutbound: async (params) => {
    return await request.download({ url: `/system/outbound/export-excel`, params })
  },

// ==================== 子表（外转部门） ====================

  // 获得外转部门列表
  getOutboundDeptListByDestId: async (destId) => {
    return await request.get({ url: `/system/outbound/outbound-dept/list-by-dest-id?destId=` + destId })
  },

  // 获得外转部门列表
  getOutboundDeptList: async () => {
    return await request.get({ url: `/system/outbound/outbound-dept/list`})
  },
}
