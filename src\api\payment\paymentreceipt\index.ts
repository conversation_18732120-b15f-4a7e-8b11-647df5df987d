import request from '@/config/axios'

// 回单付账单 VO
export interface ReceiptVO {
  id: number // id
  paymentId: string // 账单id
  paymentNo: string // 账单编号
  customerName: string // 姓名
  customerNumber: string // 会员号
  customerMobile: string // 电话
  financeDeptId: number // 收款部门id
  financeDeptName: string // 收款部门名称
  finaceDeptUser: string // 收款人员
  balanceTime: Date // 账单日期
  totalExpenses: number // 账单金额
  paidAmount: number // 支付金额
  remainAmount: number // 剩余金额
  isAudit: number // 是否审核
  auditTime: Date // 结算时间
  auditUser: string // 结算人员
  revokeUser: string // 反结算人员
  revokeTime: Date // 反结算时间
}

// 回单付账单 API
export const ReceiptApi = {
  // 查询回单付账单分页
  getReceiptPage: async (params: any) => {
    return await request.get({ url: `/payment/receipt/page`, params })
  },

  // 查询回单付账单详情
  getReceipt: async (id: number) => {
    return await request.get({ url: `/payment/receipt/get?id=` + id })
  },

  // 新增回单付账单
  createReceipt: async (data: ReceiptVO) => {
    return await request.post({ url: `/payment/receipt/create`, data })
  },

  // 修改回单付账单
  updateReceipt: async (data: ReceiptVO) => {
    return await request.put({ url: `/payment/receipt/update`, data })
  },

  // 删除回单付账单
  deleteReceipt: async (id: number) => {
    return await request.delete({ url: `/payment/receipt/delete?id=` + id })
  },

  //批量删除回款单
  batchDeleteReceipt: async (ids: number[]) => {
    return await request.delete({ url: `/payment/receipt/batch-delete`, data: ids })
  },

  // 导出回单付账单 Excel
  exportReceipt: async (params) => {
    return await request.download({ url: `/payment/receipt/export-excel`, params })
  },
  /*结算账单*/
  settleReceipt: async (id: number)=> {
    return request.post({ url: `/payment/receipt/settle?id=` + id })
  },
  /*反结算账单*/
  revokeSettleReceipt: (id: number)=> {
    return request.post({ url: `/payment/receipt/revoke-settle?id=` + id })

  },
  addPayment: (data: ReceiptVO) =>{
    console.log(data)
    return request.post({ url: `/payment/receipt/add-payment`, data })
  }
}
