import request from '@/config/axios'

// 部门回款单 VO
export interface DeptHdrVO {
  id: number // id
  paymentId: string // 回款单id
  paymentNo: string // 账单编号
  bankPaid: number // 银行转账
  paidDeptId: number // 付款部门id
  todayRemainAmount: number // 当日欠款
  totalAmountYj: number // 佣金支出
  financeDeptName: string // 收款部门名称
  chargePaid: number // 日常费用支出
  totalPaid: number // 支出合计
  appPaid: number // APP支付
  isAudit: number // 是否审核
  alipayPaid: number // 支付宝支付
  yesterdayRemainAmount: number // 昨日欠款
  totalAmount: number // 费用合计
  totalAmountXf: number // 现付合计
  totalIncome: number // 收入合计
  paidDeptTypeCode: number // 付款部门类型
  totalAmountTf: number // 提付合计
  auditTime: Date // 审核时间
  auditUser: string // 审核人员
  totalAmountDff: number // 垫付费合计
  paidDeptTypeCodeValue: string // 付款部门类型名称
  paidAmount: number // 当日付款
  totalExpenses: number // 当日应回
  chargeAmountSum: number // 送货费支出
  chargeIncome: number // 日常费用收入
  paidDeptName: string // 回款部门名称
  wechatPaid: number // 微信支付
  posPaid: number // 对公账户
  totalRemainAmount: number // 累计欠款
  totalAmountTransfer: number // 中转费支出
  balanceTime: Date // 账单日期
  totalAmountCod: number // 代收款收入
  cashPaid: number // 现金支付
  financeDeptId: number // 收款部门id
}

// 部门回款单 API
export const DeptHdrApi = {
  // 查询部门回款单分页
  getDeptHdrPage: async (params: any) => {
    return await request.get({ url: `/payment/dept-hdr/page`, params })
  },

  // 查询部门回款单详情
  getDeptHdr: async (id: number) => {
    return await request.get({ url: `/payment/dept-hdr/get?id=` + id })
  },

  // 新增部门回款单
  createDeptHdr: async (data: DeptHdrVO) => {
    return await request.post({ url: `/payment/dept-hdr/create`, data })
  },

  // 修改部门回款单
  updateDeptHdr: async (data: DeptHdrVO) => {
    return await request.put({ url: `/payment/dept-hdr/update`, data })
  },

  // 删除部门回款单
  deleteDeptHdr: async (id: number) => {
    return await request.delete({ url: `/payment/dept-hdr/delete?id=` + id })
  },

  // 导出部门回款单 Excel
  exportDeptHdr: async (params) => {
    return await request.download({ url: `/payment/dept-hdr/export-excel`, params })
  },

  //添加收款
  addPayment: async (data: DeptHdrVO)=> {
   return await request.post({ url: `/payment/dept-hdr/addPayment`, data })
  },
  // 审核回款单
  auditDeptHdr: async (data: any) => {
    return await request.post({ url: `/payment/dept-hdr/audit`, data })
  },
  // 反审核回款单
  cancelAuditDeptHdr: async (data: any) => {
    return await request.post({ url: `/payment/dept-hdr/cancelAudit`, data })
  },
}
