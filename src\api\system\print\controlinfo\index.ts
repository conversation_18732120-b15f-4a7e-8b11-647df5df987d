import request from '@/config/axios'

// 控件信息 VO
export interface ControlInfoVO {
  id: number // 自增序列
  name: string // 控件名称
  fieldName: string // 字段属性
  type: string // 控件类型
  tableName: string // 表名
  isEnable: string // 是否启用：0 否 1 是
}

// 控件信息 API
export const ControlInfoApi = {
  // 查询控件信息分页
  getControlInfoPage: async (params: any) => {
    return await request.get({ url: `/wljh/control-info/page`, params })
  },

  // 查询控件信息详情
  getControlInfo: async (id: number) => {
    return await request.get({ url: `/wljh/control-info/get?id=` + id })
  },

  // 新增控件信息
  createControlInfo: async (data: ControlInfoVO) => {
    return await request.post({ url: `/wljh/control-info/create`, data })
  },

  // 修改控件信息
  updateControlInfo: async (data: ControlInfoVO) => {
    return await request.put({ url: `/wljh/control-info/update`, data })
  },

  // 删除控件信息
  deleteControlInfo: async (id: number) => {
    return await request.delete({ url: `/wljh/control-info/delete?id=` + id })
  },

  // 导出控件信息 Excel
  exportControlInfo: async (params) => {
    return await request.download({ url: `/wljh/control-info/export-excel`, params })
  }
}