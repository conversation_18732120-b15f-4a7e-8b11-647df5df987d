import request from '@/config/axios'

// 网点信息 VO
export interface BranchVO {
  id: number // 网点ID
  branchName: string // 网点名称
  branchNo: string // 网点编号
  branchType: number // 网点类型
  branchMode: number // 合作模式
  printName: string // 打印名称
  branchQrychar: string // 快捷查询码
  parentId: number // 上级部门
  parentName: string //上级部门名称
  deptId: number // 部门ID
  zoneDeptId: number // 所属区域
  xLong: string // 经度
  yLati: string // 纬度
  receiptId: number // 回单站点
  eceiptMidId: number // 回单中转站点
  retuenId: number // 退货站点
  renounceId: number // 弃货站点
  midwayId: number // 中转站点
  specialId: number // 预付站点
  destInputMode: number // 目的地录入方式
  dlvrBranchNo: string // 库区编号
  openDate: Date // 启用日期
  closeDate: Date // 关闭日期
  feeBusinessType: number // 手续费业务类型
  businessType: number // 业务类型
  itemNamePrefix: string // 站点代码
  transportTime: string // 定日达实效
  branchStatus: boolean // 是否启用
  remark: string // 备注
}

// 网点信息 API
export const BranchApi = {
  // 查询网点信息分页
  getBranchPage: async (params: any) => {
    return await request.get({ url: `/system/branch/page`, params })
  },

  // 查询网点信息详情
  getBranch: async (id: number) => {
    return await request.get({ url: `/system/branch/get?id=` + id })
  },

  // 查询网点信息详情
  getBranchByDeptId: async (id: number) => {
    return await request.get({ url: `/system/branch/get-by-dept-id?id=` + id })
  },

  // 根据网点名称查询网点信息
  getBranchByBranchName: async(branchName: string) => {
    return await request.get({ url: `/system/branch/get-by-name?branchName=${branchName}` })
  },

  // 获取到达站
  getArriveDept: async (address: string) => {
    return await request.get({ url: `/system/branch/getArriveDept?address=` + address })
  },

  // 新增网点信息
  createBranch: async (data: BranchVO) => {
    return await request.post({ url: `/system/branch/create`, data })
  },

  // 修改网点信息
  updateBranch: async (data: BranchVO) => {
    return await request.put({ url: `/system/branch/update`, data })
  },

  // 删除网点信息
  deleteBranch: async (id: number) => {
    return await request.delete({ url: `/system/branch/delete?id=` + id })
  },

  // 导出网点信息 Excel
  exportBranch: async (params) => {
    return await request.download({ url: `/system/branch/export-excel`, params })
  },

  // 查询网点列表
  getBranchList: async (branchName) => {
    return await request.get({ url: `/system/branch/list?branchName`+ branchName })
  },

// ==================== 子表（网点银行账户） ====================

  // 根据部门ID获得网点银行账户
  getBranchBankByBranchId: async (branchId) => {
    return await request.get({ url: `/system/branch/branch-bank/get-by-branch-id?branchId=` + branchId })
  },
  
// ==================== 子表（网点联系方式） ====================
  
  // 根据部门ID获得网点联系方式
  getBranchContactByBranchId: async (branchId) => {
    return await request.get({ url: `/system/branch/branch-contact/get-by-branch-id?branchId=` + branchId })
  },

// ==================== 子表（网点费用比率） ====================
  
  // 根据部门ID获得网点费用比率
  getBranchRatioByBranchId: async (branchId) => {
    return await request.get({ url: `/system/branch/branch-ratio/get-by-branch-id?branchId=` + branchId })
  },
}
