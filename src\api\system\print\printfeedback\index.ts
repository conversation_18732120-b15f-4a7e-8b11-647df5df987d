import request from '@/config/axios'

// 打印机反馈 VO
export interface PrintFeedbackVO {
  id: number // 自增序列
  uploader: string // 上传人id
  uploaderName: string // 上传人
  uploadTime: Date // 上传时间
  photoUrl: string // 上传图片
  status: string // 状态（0-未处理 1-已处理）
  handler: string // 处理人id
  handlerName: string // 处理人
  handleTime: Date // 处理时间
  remark: string // 备注
}

// 打印机反馈 API
export const PrintFeedbackApi = {
  // 查询打印机反馈分页
  getPrintFeedbackPage: async (params: any) => {
    return await request.get({ url: `/wljh/print-feedback/page`, params })
  },

  // 查询打印机反馈详情
  getPrintFeedback: async (id: number) => {
    return await request.get({ url: `/wljh/print-feedback/get?id=` + id })
  },

  // 新增打印机反馈
  createPrintFeedback: async (data: PrintFeedbackVO) => {
    return await request.post({ url: `/wljh/print-feedback/create`, data })
  },

  // 修改打印机反馈
  updatePrintFeedback: async (data: PrintFeedbackVO) => {
    return await request.put({ url: `/wljh/print-feedback/update`, data })
  },

  // 记录打印机信息
  recordPrintFeedback: async (data: PrintFeedbackVO) => {
    return await request.post({ url: `/wljh/print-feedback/record`, data })
  },

  // 标记为已处理 - 参数中只需要id即可
  handlePrintFeedback: async (data) => {
    return await request.post({ url: `/wljh/print-feedback/handle`, data })
  },

  // 删除打印机反馈
  deletePrintFeedback: async (id: number) => {
    return await request.delete({ url: `/wljh/print-feedback/delete?id=` + id })
  },

  // 导出打印机反馈 Excel
  exportPrintFeedback: async (params) => {
    return await request.download({ url: `/wljh/print-feedback/export-excel`, params })
  }
}
