import request from '@/config/axios'

// 无主货 VO
export interface OrderUnownerVO {
  id: number //编号
  unownerNo: string // 无主货编号
  unownerStatus: number // 无主货状态
  reportBranchId: number // 上报站点ID
  reportBranchName: string // 上报站点名
  reportTime: Date // 上报时间
  goodsName: string // 货物名称
  goodsUnit: number // 货物包装
  goodsNum: number // 货物件数
  goodsWeight: number // 货物重量
  goodsVolume: number // 货物体积
  reportDesc: string // 上报描述
  reportImgUrl: string // 图片
  remark: string // 备注
}

// 无主货 API
export const OrderUnownerApi = {
  // 查询无主货分页
  getOrderUnownerPage: async (params: any) => {
    return await request.get({ url: `/oems/order-unowner/page`, params })
  },

  // 查询无主货详情
  getOrderUnowner: async (id: number) => {
    return await request.get({ url: `/oems/order-unowner/get?id=` + id })
  },

  // 新增无主货
  createOrderUnowner: async (data: OrderUnownerVO) => {
    return await request.post({ url: `/oems/order-unowner/create`, data })
  },

  // 修改无主货
  updateOrderUnowner: async (data: OrderUnownerVO) => {
    return await request.put({ url: `/oems/order-unowner/update`, data })
  },

  // 删除无主货
  deleteOrderUnowner: async (id: number) => {
    return await request.delete({ url: `/oems/order-unowner/delete?id=` + id })
  },

  // 导出无主货 Excel
  exportOrderUnowner: async (params) => {
    return await request.download({ url: `/oems/order-unowner/export-excel`, params })
  },

// ==================== 子表（无主货认领） ====================

  // 获得无主货认领列表
  getUnownerDtlListByUnownerId: async (unownerId) => {
    return await request.get({ url: `/oems/order-unowner/unowner-dtl/list-by-unowner-id?unownerId=` + unownerId })
  },
}
