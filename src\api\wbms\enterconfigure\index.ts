import request from '@/config/axios'

// 运单录入配置 VO
export interface EnterConfigureVO {
  id: number // id
  fieldName: string // 字段名
  className: string // 对应类名
  defaultValue: string // 默认值
}

// 运单录入配置 API
export const EnterConfigureApi = {
  // 查询运单录入配置分页
  getEnterConfigurePage: async (params: any) => {
    return await request.get({ url: `/wbms/enter-configure/page`, params })
  },

  // 查询运单录入配置分页
  getEnterConfigureList: async (params: any) => {
    return await request.get({ url: `/wbms/enter-configure/list`, params })
  },

  // 查询运单录入配置详情
  getEnterConfigure: async (id: number) => {
    return await request.get({ url: `/wbms/enter-configure/get?id=` + id })
  },

  // 新增运单录入配置
  createEnterConfigure: async (data: EnterConfigureVO) => {
    return await request.post({ url: `/wbms/enter-configure/create`, data })
  },

  // 修改运单录入配置
  updateEnterConfigure: async (data: EnterConfigureVO) => {
    return await request.put({ url: `/wbms/enter-configure/update`, data })
  },

  // 删除运单录入配置
  deleteEnterConfigure: async (id: number) => {
    return await request.delete({ url: `/wbms/enter-configure/delete?id=` + id })
  },

  // 导出运单录入配置 Excel
  exportEnterConfigure: async (params) => {
    return await request.download({ url: `/wbms/enter-configure/export-excel`, params })
  },
}
