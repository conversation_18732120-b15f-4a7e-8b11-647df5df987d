import request from '@/config/axios'

// 运输异常 VO
export interface OrderErrorVO {
  id: number //ID
  waybillId: number // 运单ID
  waybillNo: string // 运单编号
  errorStatus: number // 异常状态
  voyageId: number // 车次ID
  vehicleLicenseNumber: string // 车牌号
  reportBranchId: number // 上报站点ID
  reportTime: Date // 上报时间
  reportBranchName: string // 上报站点名
  reportType: number // 上报类型
  replyBranchId: number // 回复站点ID
  replyTime: Date // 回复时间
  replyBranchName: string // 回复站点名
  needReply: number // 是否需要回复
  errorNum: number // 异常件数
  errorType: number // 异常类型
  cancelReason: string // 取消原因
  finishTime: Date // 异常结束时间
  finishName: string // 异常结束操作人员
  interceptStatus: number // 是否拦截运单
  errorImgUrl: string // 异常图片
  remark: string // 备注
  errorRecords: []
}

// 运输异常 API
export const OrderErrorApi = {
  // 查询运输异常分页
  getOrderErrorPage: async (params: any) => {
    return await request.get({ url: `/oems/order-error/page`, params })
  },

  // 查询运输异常详情
  getOrderError: async (id: number) => {
    return await request.get({ url: `/oems/order-error/get?id=` + id })
  },

  // 新增运输异常
  createOrderError: async (data: OrderErrorVO) => {
    return await request.post({ url: `/oems/order-error/create`, data })
  },

  // 修改运输异常
  updateOrderError: async (data: OrderErrorVO) => {
    return await request.put({ url: `/oems/order-error/update`, data })
  },

  // 删除运输异常
  deleteOrderError: async (id: number) => {
    return await request.delete({ url: `/oems/order-error/delete?id=` + id })
  },

  // 导出运输异常 Excel
  exportOrderError: async (params) => {
    return await request.download({ url: `/oems/order-error/export-excel`, params })
  },

// ==================== 子表（异常回复） ====================

  // 获得异常回复列表
  getErrorRecordsListByOrderErrorId: async (orderErrorId) => {
    return await request.get({ url: `/oems/order-error/error-records/list-by-order-error-id?orderErrorId=` + orderErrorId })
  },
}
