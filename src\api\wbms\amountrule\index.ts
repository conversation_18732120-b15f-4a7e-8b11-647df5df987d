import request from '@/config/axios'

// 运单录入配置 VO
export interface AmountRuleVO {
  id: number // id
  amountName: string // 费用名称
  amountCode: string // 费用编码
  computeName: string // 表达式名称
  computeCode: string // 表达式编码
  ruleName: string // 规则名称
  ruleType: string // 规则类型
  ruleCode: string // 规则代码
  active: number // 部门状态（0正常 1停用）
  remark: string // 公式说明
}

// 运单录入配置 API
export const AmountRuleApi = {
  // 查询运单录入配置分页
  getAmountRulePage: async (params: any) => {
    return await request.get({ url: `/wbms/amount-rule/page`, params })
  },

  // 查询运单录入配置详情
  getAmountRule: async (id: number) => {
    return await request.get({ url: `/wbms/amount-rule/get?id=` + id })
  },

  // 新增运单录入配置
  createAmountRule: async (data: AmountRuleVO) => {
    return await request.post({ url: `/wbms/amount-rule/create`, data })
  },

  // 修改运单录入配置
  updateAmountRule: async (data: AmountRuleVO) => {
    return await request.put({ url: `/wbms/amount-rule/update`, data })
  },

  // 删除运单录入配置
  deleteAmountRule: async (id: number) => {
    return await request.delete({ url: `/wbms/amount-rule/delete?id=` + id })
  },

  // 导出运单录入配置 Excel
  exportAmountRule: async (params) => {
    return await request.download({ url: `/wbms/amount-rule/export-excel`, params })
  },
}
