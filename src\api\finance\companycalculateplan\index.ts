import request from '@/config/axios'

// 物流公司清分方案 VO
export interface CompanyCalculatePlanVO {
  id: number // 自增序列
  logisticsCompaniesId: number // 物流公司id
  calcType: string // 计算方式（1-比例 2-底价）
  rangeLowerBound: number // 区间下限
  rangeUpperBound: number // 区间上限
  startTime: Date // 有效期起
  endTime: Date // 有效期止
  companyValueRatio: number // 物流公司分成-比例
  platformValueRatio: number // 平台分成-比例
  companyValueBase: number // 物流公司分成-底价
  platformValueBase: number // 平台分成-底价
}

// 物流公司清分方案 API
export const CompanyCalculatePlanApi = {
  // 查询物流公司清分方案分页
  getCompanyCalculatePlanPage: async (params: any) => {
    return await request.get({ url: `/wljh/company-calculate-plan/page`, params })
  },

  // 查询物流公司清分方案详情
  getCompanyCalculatePlan: async (id: number) => {
    return await request.get({ url: `/wljh/company-calculate-plan/get?id=` + id })
  },

  // 新增物流公司清分方案
  createCompanyCalculatePlan: async (data: CompanyCalculatePlanVO) => {
    return await request.post({ url: `/wljh/company-calculate-plan/create`, data })
  },

  // 修改物流公司清分方案
  updateCompanyCalculatePlan: async (data: CompanyCalculatePlanVO) => {
    return await request.put({ url: `/wljh/company-calculate-plan/update`, data })
  },

  // 删除物流公司清分方案
  deleteCompanyCalculatePlan: async (id: number) => {
    return await request.delete({ url: `/wljh/company-calculate-plan/delete?id=` + id })
  },

  // 导出物流公司清分方案 Excel
  exportCompanyCalculatePlan: async (params) => {
    return await request.download({ url: `/wljh/company-calculate-plan/export-excel`, params })
  }
}