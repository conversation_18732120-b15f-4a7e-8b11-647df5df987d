<template>
  <el-form-item :label="label" :prop="propName">
    <el-select
      v-model="selectedBranchId"
      @change="handleBranchChange"
      filterable
      remote
      reserve-keyword
      :remote-method="remoteMethod"
      :placeholder="placeholder"
      :class="selectClass"
      :disabled="selectDisabled"
      :loading="branchStore.loading"
      :automatic-dropdown="true"
    >
      <el-option
        v-for="item in options"
        :key="item.id"
        :label="item.branchName"
        :value="item.deptId"
      />
    </el-select>
  </el-form-item>
</template>

<script setup>
import { ref, onMounted, watch, computed  } from 'vue'
import { ElMessage } from 'element-plus'
import { BranchApi } from '@/api/system/branch'

import { defineStore } from 'pinia'

const useBranchStore = defineStore('branch', () => {
  const branchList = ref([])
  const loading = ref(false)
  const lastFetchTime = ref(0)
  const CACHE_TIME = 30 * 60 * 1000 // 30分钟缓存

  // 保持原有 API 调用方式
  const fetchBranches = async (force = false) => {
    const now = Date.now()
    if (!force && branchList.value.length > 0 && now - lastFetchTime.value < CACHE_TIME) {
      return branchList.value
    }

    try {
      loading.value = true
      const response = await BranchApi.getBranchList() // 保持原有 API 调用
      branchList.value = response || []
      lastFetchTime.value = now
      return branchList.value
    } catch (error) {
      console.error('获取网点列表失败:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  return {
    branchList,
    loading,
    fetchBranches
  }
})

const branchStore = useBranchStore()

// 定义组件内部状态
const selectedBranchId = ref(null)
const searchQuery = ref('')

const options = computed(() => {
  if (!searchQuery.value) {
    return branchStore.branchList
  }
  return branchStore.branchList.filter(item =>
    item.branchName.toLowerCase().includes(searchQuery.value.toLowerCase())
  )
})

// 定义组件接收的外部属性
const props = defineProps({
  propName: {
    type: String,
    required: true
  },
  modelValue: {
    type: [String, Number],
    default: null
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  selectClass: {
    type: String,
    default: '!w-240px'
  },
  selectDisabled: {
    type: Boolean,
    default: false
  },
  depts: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'branch-selected'])

// 远程搜索方法
const remoteMethod = (query) => {
  searchQuery.value = query
}

// 在组件挂载完成后获取网点列表
onMounted(async () => {
  try {
    if (props.depts && props.depts.length > 0) {
      branchStore.branchList = props.depts
    } else {
      await branchStore.fetchBranches() // 使用 store 中的方法
    }

    if (props.modelValue) {
      selectedBranchId.value = props.modelValue
    }
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败，请稍后再试！')
  }
})

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (newVal) => {
    selectedBranchId.value = newVal
  }
)

// 处理分支选择变化
const handleBranchChange = (value) => {
  const selectedBranch = branchStore.branchList.find(item => item.deptId === value)
  if (selectedBranch) {
    emit('update:modelValue', value)
    emit('branch-selected', {
      branchType: selectedBranch.branchType,
      parentId: selectedBranch.parentId,
      branchId: selectedBranch.deptId,
      branchName: selectedBranch.branchName,
      branchDeliveryProportion: selectedBranch.branchRatio?.deliveryProportion
    })
  }
}

</script>

<style scoped>
/* 移除原有边框和阴影，添加下划线 */
:deep(.el-select__wrapper) {
  box-shadow:  0 0 0 0  !important;
  border-radius: 0 !important;
  border-bottom: 1px solid #dcdfe6 !important;
  padding: 0 !important;
  background-color: transparent !important;
}

</style>
