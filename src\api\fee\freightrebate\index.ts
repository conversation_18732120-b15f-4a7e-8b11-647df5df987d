import request from '@/config/axios'

// 运费折扣 VO
export interface FreightRebateVO {
  id: number // id
  rebateType: string // 折扣类型对象
  startBranch: number // 始发区域
  endBranch: number // 到达区域
  rebateName: string // 折扣名称
  rateValue: number // 折扣系数
  effectiveDate: Date // 生效时间
  expirationDate: Date // 失效时间
  remark: string // 备注
  weightLower: number // 重量下限
  weightUpper: number // 重量上限
  startDeptId: number // 始发网点
  endDeptId: number // 到达网点
}

// 运费折扣 API
export const FreightRebateApi = {
  // 查询运费折扣分页
  getFreightRebatePage: async (params: any) => {
    return await request.get({ url: `/fee/freight-rebate/page`, params })
  },

  // 查询运费折扣详情
  getFreightRebate: async (id: number) => {
    return await request.get({ url: `/fee/freight-rebate/get?id=` + id })
  },

  // 新增运费折扣
  createFreightRebate: async (data: FreightRebateVO) => {
    return await request.post({ url: `/fee/freight-rebate/create`, data })
  },

  // 修改运费折扣
  updateFreightRebate: async (data: FreightRebateVO) => {
    return await request.put({ url: `/fee/freight-rebate/update`, data })
  },

  // 删除运费折扣
  deleteFreightRebate: async (id: number) => {
    return await request.delete({ url: `/fee/freight-rebate/delete?id=` + id })
  },

  // 导出运费折扣 Excel
  exportFreightRebate: async (params) => {
    return await request.download({ url: `/fee/freight-rebate/export-excel`, params })
  },
}
