import request from '@/config/axios'

// 充值申请 VO
export interface RechargeApplyVO {
  id: number // 自增序列
  logisticsNetworkId: number // 网点id
  accountId: number // 账户id
  rechargeAmount: number // 充值金额
  certUrl: string // 支付凭证
  applyUserId: number // 申请人id
  applyTime: Date // 申请时间
  applyStatus: string // 申请状态（1-待审核 2-审核通过 3-审核不通过 4-已充值）
  auditUserId: number // 审核人id
  auditTime: Date, // 审核时间
  auditRemark: string // 审核备注
}

/**
 * 申请状态（1-待审核 2-审核通过 3-审核不通过 4-已充值）
 */
export const RECHARGE_APPLY_STATUS_LIST = reactive([
  { label: '待审核', value: '1' },
  { label: '审核通过', value: '2' },
  { label: '审核不通过', value: '3' },
  { label: '已充值', value: '4' }
])

export const getRechargeApplyStatus = (type: string) => {
  if (!type) {
    return ''
  }
  return RECHARGE_APPLY_STATUS_LIST.find(item => item.value === type)?.label
}

// 充值申请 API
export const RechargeApplyApi = {
  // 查询充值申请分页
  getRechargeApplyPage: async (params: any) => {
    return await request.get({ url: `/wljh/recharge-apply/page`, params })
  },

  // 查询充值申请详情
  getRechargeApply: async (id: number) => {
    return await request.get({ url: `/wljh/recharge-apply/get?id=` + id })
  },

  // 新增充值申请
  createRechargeApply: async (data: RechargeApplyVO) => {
    return await request.post({ url: `/wljh/recharge-apply/create`, data })
  },

  // 修改充值申请
  updateRechargeApply: async (data: any) => {
    return await request.put({ url: `/wljh/recharge-apply/update`, data })
  },

  // 删除充值申请
  deleteRechargeApply: async (id: number) => {
    return await request.delete({ url: `/wljh/recharge-apply/delete?id=` + id })
  },

  // 导出充值申请 Excel
  exportRechargeApply: async (params) => {
    return await request.download({ url: `/wljh/recharge-apply/export-excel`, params })
  },

  // 审核充值申请
  auditRechargeApply: async (data: any) => {
    return await request.post({ url: `/wljh/recharge-apply/audit`, data })
  },

  // 充值确认
  confirmRechargeApply: async (data: any) => {
    return await request.post({ url: `/wljh/recharge-apply/confirm`, data })
  }
}
