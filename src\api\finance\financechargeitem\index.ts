import request from '@/config/axios'

// 核销项目 VO
export interface ChargeItemVO {
  chargeCode: string // 核销项目编号
  chargeItemType: string // 核销项目类型
  chargeName: string // 核销项目名称
  paidType: string // 支出类型
  dataSourceType: string // 数据来源
  remark: string // 备注
  status: string // 启用状态
  chargeNode: string // 核销触发节点
}

// 核销项目 API
export const ChargeItemApi = {
  // 查询核销项目分页
  getChargeItemPage: async (params: any) => {
    return await request.get({ url: `/finance/charge-item/page`, params })
  },
  getChargeItemList: async () => {
    return await request.get({ url: `/finance/charge-item/list` })
  },
  // 查询核销项目详情
  getChargeItem: async (id: number) => {
    return await request.get({ url: `/finance/charge-item/get?id=` + id })
  },

  // 新增核销项目
  createChargeItem: async (data: ChargeItemVO) => {
    return await request.post({ url: `/finance/charge-item/create`, data })
  },

  // 修改核销项目
  updateChargeItem: async (data: ChargeItemVO) => {
    return await request.put({ url: `/finance/charge-item/update`, data })
  },

  // 删除核销项目
  deleteChargeItem: async (id: number) => {
    return await request.delete({ url: `/finance/charge-item/delete?id=` + id })
  },

  // 导出核销项目 Excel
  exportChargeItem: async (params) => {
    return await request.download({ url: `/finance/charge-item/export-excel`, params })
  },
}
