import request from '@/config/axios'

// 库存记录 VO
export interface StockRecordVO {
  id: number // id
  deptId: number // 网点ID
  deptName: string // 网点名称
  waybillId: number // 运单ID
  waybillNo: string // 运单编号
  waybillStatus: string // 运单状态
  startDeptId: number // 运单起始网点ID
  startDeptName: string // 运单起始网点名称
  endDeptId: number // 运单到达网点ID
  endDeptName: string // 运单到达网点名称
  stockStatus: number // 库存状态（0：已入库:1：已出库）
  stockType: number //入库状态
  godownNo: string // 库区编号
  godownName: string // 库区名称
  remark: string // 备注
}

// 库存记录 API
export const StockRecordApi = {
  // 查询库存记录分页
  getStockRecordPage: async (params: any) => {
    return await request.get({ url: `/wbms/stock-record/page`, params })
  },

  // 查询库存记录详情
  getStockRecord: async (id: number) => {
    return await request.get({ url: `/wbms/stock-record/get?id=` + id })
  },

  // 新增库存记录
  createStockRecord: async (data: StockRecordVO) => {
    return await request.post({ url: `/wbms/stock-record/create`, data })
  },

  // 修改库存记录
  updateStockRecord: async (data: StockRecordVO) => {
    return await request.put({ url: `/wbms/stock-record/update`, data })
  },

  // 删除库存记录
  deleteStockRecord: async (id: number) => {
    return await request.delete({ url: `/wbms/stock-record/delete?id=` + id })
  },

  // 导出库存记录 Excel
  exportStockRecord: async (params) => {
    return await request.download({ url: `/wbms/stock-record/export-excel`, params })
  },
}
