<template>
  <el-form-item :label="label" :prop="propName">
    <el-select
      v-model="selectedVehicleId"
      @change="handleVehicleChange"
      filterable
      remote
      reserve-keyword
      :remote-method="remoteMethod"
      :placeholder="placeholder"
      :class="selectClass"
      :disabled="selectDisabled"
    >
      <el-option
        v-for="item in options"
        :key="item.id"
        :label="item.vehicleLicenseNumber"
        :value="item.id"
      />
    </el-select>
  </el-form-item>
</template>
  
  <script setup>
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { VehiclesApi } from '@/api/vms/vehicles'

// 定义选项列表
const options = ref([])
const selectedVehicleId = ref(null) // 用于存储选中的车辆ID
const loading = ref(false) // 用于控制加载状态
const list = ref([]) // 用于存储从服务器获取的数据

// 定义组件接收的外部属性
const props = defineProps({
  propName: {
    type: String,
    required: true
  },
  modelValue: {
    type: [String, Number],
    default: null
  },
  label: {
    type: String,
    default: '车辆选择'
  },
  placeholder: {
    type: String,
    default: '请输入车辆名称'
  },
  selectClass: {
    type: String,
    default: '!w-240px'
  },
  selectDisabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'vehicle-selected'])

// 远程搜索方法
const remoteMethod = (query) => {
  if (query) {
    loading.value = true
    setTimeout(() => {
      loading.value = false
      options.value = list.value.filter((item) => {
        return item.vehicleLicenseNumber.toLowerCase().includes(query.toLowerCase())
      })
    }, 200)
  } else {
    options.value = []
  }
}

// 在组件挂载完成后获取车辆列表
onMounted(async () => {
  try {
    const response = await VehiclesApi.getVehicleList()

    list.value = response || []
    options.value = list.value

  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败，请稍后再试！')
  }
})

// 监听 modelValue 变化并更新 selectedVehicleId 和 selectedVehicleName
watch(
  () => props.modelValue,
  (newVal) => {
    selectedVehicleId.value = newVal
  }
)


// 处理车辆选择变更
const handleVehicleChange = (value) => {
  const selectedVehicle = list.value.find((item) => item.id === value)
  if (selectedVehicle) {
    emit('update:modelValue', value)
    emit('vehicle-selected', {
      vehicleId: selectedVehicle.id,
      vehicleLicenseNumber: selectedVehicle.vehicleLicenseNumber,
      vehicleType: selectedVehicle.vehicleType
    })
  }
}
</script>
  