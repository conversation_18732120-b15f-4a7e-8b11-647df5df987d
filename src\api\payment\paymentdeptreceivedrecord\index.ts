import request from '@/config/axios'

// 回款记录 VO
export interface ReceivedRecordVO {
  id: number // id
  paymentId: string // 账单ID
  paidChannel: string // 支付渠道
  receivedUser: string // 收款用户
  financeDeptId: number // 收款部门
  financeDeptName: string // 收款部门名称
  receivedAmount: number // 收款金额
  paidDeptId: number // 回款部门id
  paidDeptName: string // 回款部门名称
  remark: string // 备注
}

// 回款记录 API
export const ReceivedRecordApi = {
  // 查询回款记录分页
  getReceivedRecordPage: async (params: any) => {
    return await request.get({ url: `/payment/received-record/page`, params })
  },
  // 查询回款记录详情
  getReceivedRecords: async (paymentId: string) => {
    return await request.get({ url: `/payment/received-record/list?paymentId=` + paymentId })
  },

  // 新增回款记录
  createReceivedRecord: async (data: ReceivedRecordVO) => {
    return await request.post({ url: `/payment/received-record/create`, data })
  },

  // 修改回款记录
  updateReceivedRecord: async (data: ReceivedRecordVO) => {
    return await request.put({ url: `/payment/received-record/update`, data })
  },

  // 删除回款记录
  deleteReceivedRecord: async (data: any) => {
    return await request.post({ url: `/payment/received-record/revoke`, data })
  },

  // 导出回款记录 Excel
  exportReceivedRecord: async (params) => {
    return await request.download({ url: `/payment/received-record/export-excel`, params })
  },
}
