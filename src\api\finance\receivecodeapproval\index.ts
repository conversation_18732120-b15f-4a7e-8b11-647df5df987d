import request from '@/config/axios'

// 收款码审核 VO
export interface ReceiveCodeApprovalVO {
  id: number // 自增序列
  accountRole: string // 账户角色（1- 平台 2-网点 3-物流公司）
  mainId: number // 网点/物流公司ID
  approvalStatus: string // 审核状态（0-待审核 1-审核通过 2-审核不通过）
  receiveOne: string // 收款码-1k
  receiveTwo: string // 收款码-2k
  receiveThree: string // 收款码-3k
  receiveFive: string // 收款码-5k
}

// 收款码审核 API
export const ReceiveCodeApprovalApi = {
  // 查询收款码审核分页
  getReceiveCodeApprovalPage: async (params: any) => {
    return await request.get({ url: `/wljh/receive-code-approval/page`, params })
  },

  // 查询收款码审核详情
  getReceiveCodeApproval: async (id: number) => {
    return await request.get({ url: `/wljh/receive-code-approval/get?id=` + id })
  },

  // 新增收款码审核
  createReceiveCodeApproval: async (data: ReceiveCodeApprovalVO) => {
    return await request.post({ url: `/wljh/receive-code-approval/create`, data })
  },

  // 修改收款码审核
  updateReceiveCodeApproval: async (data: ReceiveCodeApprovalVO) => {
    return await request.put({ url: `/wljh/receive-code-approval/update`, data })
  },

  // 删除收款码审核
  deleteReceiveCodeApproval: async (id: number) => {
    return await request.delete({ url: `/wljh/receive-code-approval/delete?id=` + id })
  },

  // 导出收款码审核 Excel
  exportReceiveCodeApproval: async (params) => {
    return await request.download({ url: `/wljh/receive-code-approval/export-excel`, params })
  },

  // 审核
  approvalReceiveCodeApproval: async (data: ReceiveCodeApprovalVO) => {
    return await request.put({ url: `/wljh/receive-code-approval/approval`, data })
  },
}
