import request from '@/config/axios'

// 运费方案管理 VO
export interface FreightSchemeVO {
  id: number // id
  effectiveDate: Date // 生效日期
  expirationDate: Date // 失效日期
  customerId: number // 客户id
  sendType: string // 分段类型
  billAreaId: number // 始发区域
  discAreaId: number // 到达区域
  computeType: string // 计算方式
  singlePrice: number // 单票价格
  remark: string // 描述
  deptId: number // 部门id
  userId: number // 用户id
  schemeName: string // 方案名称
  schemeNo: string // 方案编号
  schemeType: string // 方案类型
  schemeRule: string // 方案规则
  schemeDetailList: []
}

export interface OmsGoodsDO {
  goodsNo: number
  goodsName: string
  goodsPackageUnit: string
  goodsNum: number 
  goodsVolume: number 
  goodsWeight: number 
  goodsPrice: number 
}

// 运费方案管理 API
export const FreightSchemeApi = {
  // 查询运费方案管理分页
  getFreightSchemePage: async (params: any) => {
    return await request.get({ url: `/fee/freight-scheme/page`, params })
  },

  // 查询运费方案管理详情
  getFreightSchemeById: async (id: number) => {
    return await request.get({ url: `/fee/freight-scheme/get?id=` + id })
  },

  // 查询运费方案详情
  getFreightSchemeByDeptId: async (loadDeptId: number,discDeptId: number) => {
    return await request.get({ url: `/fee/freight-scheme/getFreightSchemeByDeptId`, params:{loadDeptId, discDeptId}})
  },

  getFreightScheme: async () => {
    return await request.get({ url: `/fee/freight-scheme/getFreightScheme`})
  },

  // 新增运费方案管理
  createFreightScheme: async (data: FreightSchemeVO) => {
    return await request.post({ url: `/fee/freight-scheme/create`, data })
  },

  // 修改运费方案管理
  updateFreightScheme: async (data: FreightSchemeVO) => {
    return await request.put({ url: `/fee/freight-scheme/update`, data })
  },

  // 删除运费方案管理
  deleteFreightScheme: async (id: number) => {
    return await request.delete({ url: `/fee/freight-scheme/delete?id=` + id })
  },

  // 导出运费方案管理 Excel
  exportFreightScheme: async (params) => {
    return await request.download({ url: `/fee/freight-scheme/export-excel`, params })
  },

  // 根据方案计算运费
  computeFreight: async (id: number, list: OmsGoodsDO[]) => {
    return await request.post({ url: `/fee/freight-scheme/compute`, data: { id, list } })
  }
}
