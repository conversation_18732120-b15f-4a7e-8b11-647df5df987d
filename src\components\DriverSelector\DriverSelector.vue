<template>
  <el-form-item :label="label" :prop="propName">
    <el-select
      v-model="selectedDriverId"
      @change="handleDriverChange"
      filterable
      remote
      reserve-keyword
      :remote-method="remoteMethod"
      :placeholder="placeholder"
      :class="selectClass"
      :disabled="selectDisabled"
    >
      <el-option v-for="item in options" :key="item.id" :label="item.driverName" :value="item.id" />
    </el-select>
  </el-form-item>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { DriverApi } from '@/api/vms/driver'


// 定义选项列表
const options = ref([])
const selectedDriverId = ref(null) // 用于存储选中的驾驶员ID
const loading = ref(false) // 用于控制加载状态
const list = ref([]) // 用于存储从服务器获取的数据

// 定义组件接收的外部属性
const props = defineProps({
  propName: {
    type: String,
    required: true
  },
  modelValue: {
    type: [String, Number],
    default: null
  },
  label: {
    type: String,
    default: '驾驶员选择'
  },
  placeholder: {
    type: String,
    default: '请输入驾驶员姓名'
  },
  selectClass: {
    type: String,
    default: '!w-240px'
  },
  selectDisabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'driver-selected'])

// 远程搜索方法
const remoteMethod = (query) => {
  if (query) {
    loading.value = true
    setTimeout(() => {
      loading.value = false
      options.value = list.value.filter((item) => {
        return item.driverName.toLowerCase().includes(query.toLowerCase())
      })
    }, 200)
  } else {
    options.value = []
  }
}

// 在组件挂载完成后获取驾驶员列表
onMounted(async () => {
  try {
    const response = await DriverApi.getDriverList()

    list.value = response || []
    options.value = list.value

  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败，请稍后再试！')
  }
})


// 监听 modelValue 变化并更新 selectedDriverId
watch(
  () => props.modelValue,
  (newVal) => {
    selectedDriverId.value = newVal
  }
)

// 处理驾驶员选择变更
const handleDriverChange = (value) => {
  const selectedDriver = list.value.find(item => item.id === value)
  if (selectedDriver) {
    emit('update:modelValue', value)
    emit('driver-selected', {
      driverId: selectedDriver.id,
      driverName: selectedDriver.driverName,
      contactMobile: selectedDriver.contactMobile
    })
  }
}
</script>
  