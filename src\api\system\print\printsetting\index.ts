import request from '@/config/axios'

// 打印机配置 VO
export interface PrintSettingVO {
  id: number // 自增序列
  printerType: string // 打印类型
  printerName: string // 打印机名称
  account: string // 账号
}

// 打印机配置 API
export const PrintSettingApi = {
  // 查询打印机配置分页
  getPrintSettingPage: async (params: any) => {
    return await request.get({ url: `/wljh/print-setting/page`, params })
  },

  // 查询打印机配置详情
  getPrintSetting: async (id: number) => {
    return await request.get({ url: `/wljh/print-setting/get?id=` + id })
  },

  // 新增打印机配置
  createPrintSetting: async (data: any) => {
    return await request.post({ url: `/wljh/print-setting/create`, data })
  },

  // 修改打印机配置
  updatePrintSetting: async (data: PrintSettingVO) => {
    return await request.put({ url: `/wljh/print-setting/update`, data })
  },

  // 删除打印机配置
  deletePrintSetting: async (id: number) => {
    return await request.delete({ url: `/wljh/print-setting/delete?id=` + id })
  },

  // 导出打印机配置 Excel
  exportPrintSetting: async (params) => {
    return await request.download({ url: `/wljh/print-setting/export-excel`, params })
  },

  // 批量创建打印机配置
  createAllPrintSetting: async (data: any) => {
    return await request.post({ url: `/wljh/print-setting/create-all`, data })
  },

  // 查询打印机配置
  getPrintSettingInfo: async (params: any) => {
    return await request.get({ url: `/wljh/print-setting/find-info`, params })
  }
}
