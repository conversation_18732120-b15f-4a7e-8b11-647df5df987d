import request from '@/config/axios'

// 承运商 VO
export interface TransferCarrierVO {
  id: number // 编号
  carrierCode: string // 承运商编号
  carrierName: string // 承运商名称
  ownerBranchId: number // 所属网点ID
  managerName: string // 承运商负责人
  managerTel: string // 负责人电话
  managerMobile: string // 负责人手机
  carrierType: number // 承运商类型
  carrierLevel: number // 承运商等级
  carrierAddress: number // 省市区
  carrierDistrict: string // 详细地址
  amountPtList: number // 允许结算方式
  defaultAmountPt: number // 默认结算方式
  carrierBillDeptname: string // 发站部门
  carrierBillManagername: string // 发站负责人
  carrierBillManagermobile: string // 发站负责人电话
  carrierDiscDeptname: string // 到站部门
  carrierDiscManagername: string // 到站负责人
  carrierDiscManagermobile: string // 到站负责人电话
  remark: string // 备注
}

// 承运商 API
export const TransferCarrierApi = {
  // 查询承运商分页
  getTransferCarrierPage: async (params: any) => {
    return await request.get({ url: `/system/transfer-carrier/page`, params })
  },

    // 查询承运商列表
  getTransferCarrierList: async () => {
    return await request.get({ url: `/system/transfer-carrier/list`})
  },

  // 查询承运商详情
  getTransferCarrier: async (id: number) => {
    return await request.get({ url: `/system/transfer-carrier/get?id=` + id })
  },

  // 新增承运商
  createTransferCarrier: async (data: TransferCarrierVO) => {
    return await request.post({ url: `/system/transfer-carrier/create`, data })
  },

  // 修改承运商
  updateTransferCarrier: async (data: TransferCarrierVO) => {
    return await request.put({ url: `/system/transfer-carrier/update`, data })
  },

  // 删除承运商
  deleteTransferCarrier: async (id: number) => {
    return await request.delete({ url: `/system/transfer-carrier/delete?id=` + id })
  },

  // 导出承运商 Excel
  exportTransferCarrier: async (params) => {
    return await request.download({ url: `/system/transfer-carrier/export-excel`, params })
  },

// ==================== 子表（承运商部门） ====================

  // 获得承运商部门分页
  getTransferBranchPage: async (params) => {
    return await request.get({ url: `/system/transfer-carrier/transfer-branch/page`, params })
  },

    // 查询承运商部门列表
  getTransferBranchList: async (carrierId) => {
    if (carrierId !== undefined) {
      // 如果传入了 carrierId 参数
      return await request.get({ url: `/system/transfer-carrier/transfer-branch/list?carrierId=${carrierId}` });
  } else {
      // 如果没有传入参数
      return await request.get({ url: `/system/transfer-carrier/transfer-branch/list` });
  }
  
  },
  // 新增承运商部门
  createTransferBranch: async (data) => {
    return await request.post({ url: `/system/transfer-carrier/transfer-branch/create`, data })
  },

  // 修改承运商部门
  updateTransferBranch: async (data) => {
    return await request.put({ url: `/system/transfer-carrier/transfer-branch/update`, data })
  },

  // 删除承运商部门
  deleteTransferBranch: async (id: number) => {
    return await request.delete({ url: `/system/transfer-carrier/transfer-branch/delete?id=` + id })
  },

  // 获得承运商部门
  getTransferBranch: async (id: number) => {
    return await request.get({ url: `/system/transfer-carrier/transfer-branch/get?id=` + id })
  },


}
