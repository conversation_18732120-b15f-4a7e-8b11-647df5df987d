import request from '@/config/axios'

// 车人信息 VO
export interface VehiclesDriverVO {
  id: number // 车辆司机编号
  vehicleLicenseNumber: string // 车牌号码
  driverName: string // 司机姓名
  contactMobile: string // 联系电话
  identityCardNumber: string // 身份证号
  remark: string // 备注
}

// 车人信息 API
export const VehiclesDriverApi = {
  // 查询车人信息分页
  getVehiclesDriverPage: async (params: any) => {
    return await request.get({ url: `/vms/vehicles-driver/page`, params })
  },

  // 查询车人信息详情
  getVehiclesDriver: async (id: number) => {
    return await request.get({ url: `/vms/vehicles-driver/get?id=` + id })
  },

  //获取车人列表
  getVehiclesDriverList: async () => {
    return await request.get({ url: '/vms/vehicles-driver/get-list'});
  },

  // 新增车人信息
  createVehiclesDriver: async (data: VehiclesDriverVO) => {
    return await request.post({ url: `/vms/vehicles-driver/create`, data })
  },

  // 修改车人信息
  updateVehiclesDriver: async (data: VehiclesDriverVO) => {
    return await request.put({ url: `/vms/vehicles-driver/update`, data })
  },

  // 删除车人信息
  deleteVehiclesDriver: async (id: number) => {
    return await request.delete({ url: `/vms/vehicles-driver/delete?id=` + id })
  },

  // 导出车人信息 Excel
  exportVehiclesDriver: async (params) => {
    return await request.download({ url: `/vms/vehicles-driver/export-excel`, params })
  },

  //根据条件获取车人
  getVehiclesOrDriver: async (params: any) => {
    return await request.get({ url: '/vms/vehicles-driver/get-vehicles-or-driver', params });
  }

}
