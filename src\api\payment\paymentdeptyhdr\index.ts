import request from '@/config/axios'

// 应回款统计 VO
export interface DeptYhdrVO {
  id: number // id
  paymentId: string // 回款单id
  paymentNo: string // 账单编号
  deptName: string // 回款部门名称
  deptId: number // 回款部门id
  balanceTime: Date // 账单日期
  totalExpenses: number // 当日应回
  paidAmount: number // 当日付款
  todayRemainAmount: number // 当日欠款
  yesterdayRemainAmount: number // 昨日欠款
  totalRemainAmount: number // 累计欠款
  totalAmount: number // 费用合计
  totalIncome: number // 收入合计
  totalPaid: number // 支出合计
  totalAmountXf: number // 现付合计
  totalAmountTf: number // 提付合计
  totalAmountCod: number // 代收款收入
  chargeIncome: number // 日常费用收入
  totalAmountYj: number // 佣金支出
  chargeAmountSum: number // 送货费支出
  totalAmountTransfer: number // 中转费支出
  chargePaid: number // 日常费用支出
  isAudit: number // 是否审核
  paidDeptTypeCode: number // 付款部门类型
  totalAmountDff: number // 垫付费合计
  paidDeptTypeCodeValue: string // 网点区域
}

// 应回款统计 API
export const DeptYhdrApi = {
  // 查询应回款统计分页
  getDeptYhdrPage: async (params: any) => {
    return await request.get({ url: `/payment/dept-yhdr/page`, params })
  },

  // 查询应回款统计详情
  getDeptYhdr: async (id: number) => {
    return await request.get({ url: `/payment/dept-yhdr/get?id=` + id })
  },

  // 新增应回款统计
  createDeptYhdr: async (data: DeptYhdrVO) => {
    return await request.post({ url: `/payment/dept-yhdr/create`, data })
  },

  // 修改应回款统计
  updateDeptYhdr: async (data: DeptYhdrVO) => {
    return await request.put({ url: `/payment/dept-yhdr/update`, data })
  },

  // 删除应回款统计
  deleteDeptYhdr: async (id: number) => {
    return await request.delete({ url: `/payment/dept-yhdr/delete?id=` + id })
  },

  // 导出应回款统计 Excel
  exportDeptYhdr: async (params) => {
    return await request.download({ url: `/payment/dept-yhdr/export-excel`, params })
  },
}
