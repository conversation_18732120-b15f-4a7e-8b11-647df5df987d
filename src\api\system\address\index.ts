import request from '@/config/axios'

// 地址库 VO
export interface AddressVO {
  id: number // 自增序列
  oneNo: string // 一级编号
  oneName: string // 一级名称
  twoNo: string // 二级编号
  twoName: string // 二级名称
  threeNo: string // 三级编号
  threeName: string // 三级名称
  fourNo: string // 四级编号
  fourName: string // 四级名称
  fiveNo: string // 五级编号
  fiveName: string // 五级名称
  level: number // 级别
}

// 地址库 API
export const AddressApi = {
  // 查询地址库分页
  getAddressPage: async (params: any) => {
    return await request.get({ url: `/dlog/address/page`, params })
  },

  // 查询地址库详情
  getAddress: async (id: number) => {
    return await request.get({ url: `/dlog/address/get?id=` + id })
  },

  // 新增地址库
  createAddress: async (data: AddressVO) => {
    return await request.post({ url: `/dlog/address/create`, data })
  },

  // 修改地址库
  updateAddress: async (data: AddressVO) => {
    return await request.put({ url: `/dlog/address/update`, data })
  },

  // 删除地址库
  deleteAddress: async (id: number) => {
    return await request.delete({ url: `/dlog/address/delete?id=` + id })
  },

  // 导出地址库 Excel
  exportAddress: async (params) => {
    return await request.download({ url: `/dlog/address/export-excel`, params })
  },

  // 级联查询地址库
  levelSearch: async (level: number, parentNo: string) => {
    return await request.get({ url: `/dlog/address/levelSearch?level=` + level + `&parentNo=` + parentNo})
  },

  // 模糊查询地址库
  likeSearch: async (searchValue: string, limit: number) => {
    return await request.get({ url: `/dlog/address/likeSearch?searchValue=` + searchValue + `&limit=` + limit})
  },
}
