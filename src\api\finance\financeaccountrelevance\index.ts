import request from '@/config/axios'

// 核销项目关系 VO
export interface AccountRelevanceVO {
  id: number // id
  accountCode: string // 科目编号
  accountName: string, //科目名称
  chargeCode: string // 项目编号
  chargeName: string, //项目名称
  active: string // 是否有效 1：有效；0：无效
  remark: string // 备注
  companyId: number // 公司编号
}

// 核销项目关系 API
export const AccountRelevanceApi = {
  // 查询核销项目关系分页
  getAccountRelevancePage: async (params: any) => {
    return await request.get({ url: `/finance/account-relevance/page`, params })
  },
  getAccountRelevanceList: async (params: any) => {
    return await request.get({ url: `/finance/account-relevance/list`, params })
  },
  // 查询核销项目关系详情
  getAccountRelevance: async (id: number) => {
    return await request.get({ url: `/finance/account-relevance/get?id=` + id })
  },

  // 新增核销项目关系
  createAccountRelevance: async (data: AccountRelevanceVO) => {
    return await request.post({ url: `/finance/account-relevance/create`, data })
  },

  // 修改核销项目关系
  updateAccountRelevance: async (data: AccountRelevanceVO) => {
    return await request.put({ url: `/finance/account-relevance/update`, data })
  },

  // 删除核销项目关系
  deleteAccountRelevance: async (id: number) => {
    return await request.delete({ url: `/finance/account-relevance/delete?id=` + id })
  },

  // 导出核销项目关系 Excel
  exportAccountRelevance: async (params) => {
    return await request.download({ url: `/finance/account-relevance/export-excel`, params })
  },
}
