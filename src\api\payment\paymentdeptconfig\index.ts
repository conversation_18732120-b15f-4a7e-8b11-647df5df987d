import request from '@/config/axios'

// 部门回款配置 VO
export interface DeptConfigVO {
  id: number|undefined // id
  receDeptId: number | undefined // 收款部门id
  receDeptName: string | undefined // 收款部门名称
  paidDeptId: number | undefined  // 付款部门id
  paidDeptName: string | undefined // 付款部门名称
  actCodeList: string| undefined // 回款配置
}

// 部门回款配置 API
export const DeptConfigApi = {
  // 查询部门回款配置分页
  getDeptConfigPage: async (params: any) => {
    return await request.get({ url: `/payment/dept-config/page`, params })
  },

  // 查询部门回款配置详情
  getDeptConfig: async (id: number) => {
    return await request.get({ url: `/payment/dept-config/get?id=` + id })
  },

  // 新增部门回款配置
  createDeptConfig: async (data: DeptConfigVO) => {
    return await request.post({ url: `/payment/dept-config/create`, data })
  },

  // 修改部门回款配置
  updateDeptConfig: async (data: DeptConfigVO) => {
    return await request.put({ url: `/payment/dept-config/update`, data })
  },

  // 删除部门回款配置
  deleteDeptConfig: async (id: number) => {
    return await request.delete({ url: `/payment/dept-config/delete?id=` + id })
  },

  // 导出部门回款配置 Excel
  exportDeptConfig: async (params) => {
    return await request.download({ url: `/payment/dept-config/export-excel`, params })
  },
}
