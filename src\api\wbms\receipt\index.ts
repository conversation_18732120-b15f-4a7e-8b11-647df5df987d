import request from '@/config/axios'

// 回单信息 VO
export interface ReceiptInfoVO {
  id: number // 主键
  waybillCode: string // 运单号
  receiptCode: string // 回单号
  receiptType: string // 回单类型
  receiptStatus: string // 回单状态
  receiptSign: string // 签署人
  signTime: Date // 签署时间
  signPhone: string // 签署人电话
  receiptReceive: string // 领取人
  receiveTime: Date // 领取时间
  receivePhone: string // 领取人电话
  remark: string // 备注
}

// 回单信息 API
export const ReceiptInfoApi = {
  // 查询回单信息分页
  getReceiptInfoPage: async (params: any) => {
    return await request.get({ url: `/com/receipt-info/page`, params })
  },

  // 查询回单信息详情
  getReceiptInfo: async (id: number) => {
    return await request.get({ url: `/com/receipt-info/get?id=` + id })
  },

  // 新增回单信息
  createReceiptInfo: async (data: ReceiptInfoVO) => {
    return await request.post({ url: `/com/receipt-info/create`, data })
  },

  // 修改回单信息
  updateReceiptInfo: async (data: ReceiptInfoVO) => {
    return await request.put({ url: `/com/receipt-info/update`, data })
  },

  // 删除回单信息
  deleteReceiptInfo: async (id: number) => {
    return await request.delete({ url: `/com/receipt-info/delete?id=` + id })
  },

  // 导出回单信息 Excel
  exportReceiptInfo: async (params) => {
    return await request.download({ url: `/com/receipt-info/export-excel`, params })
  },

  // 根据运单号获取回单信息
  getReceiptInfoByWaybillCode: async (waybillCode: string) => {
    return await request.get({ url: `/com/receipt-info/get-receipt-by-waybillCode?waybillCode=` + waybillCode })
  },
}


// 回单状态跟踪信息 VO
export interface ReceiptTrackVO {
  id: number // 主键
  receiptCode: string // 回单号
  nodeType: string // 节点类型
  nodeName: string // 节点名称
  nodeTime: Date // 节点时间
  nodeContent: string // 节点内容
  opUserName: string // 操作人名称
}

// 回单状态跟踪信息 API
export const ReceiptTrackApi = {
  // 查询回单状态跟踪信息分页
  getReceiptTrackPage: async (params: any) => {
    return await request.get({ url: `/com/receipt-track/page`, params })
  },

  // 查询回单状态跟踪信息详情
  getReceiptTrack: async (id: number) => {
    return await request.get({ url: `/com/receipt-track/get?id=` + id })
  },

  // 新增回单状态跟踪信息
  createReceiptTrack: async (data: ReceiptTrackVO) => {
    return await request.post({ url: `/com/receipt-track/create`, data })
  },

  // 修改回单状态跟踪信息
  updateReceiptTrack: async (data: ReceiptTrackVO) => {
    return await request.put({ url: `/com/receipt-track/update`, data })
  },

  // 删除回单状态跟踪信息
  deleteReceiptTrack: async (data: ReceiptTrackVO) => {
    return await request.post({ url: `/com/receipt-track/delete`, data })
  },

  // 导出回单状态跟踪信息 Excel
  exportReceiptTrack: async (params) => {
    return await request.download({ url: `/com/receipt-track/export-excel`, params })
  },
}

// 回单运输信息 VO
export interface ReceiptTransportVO {
  id: number // 主键
  receiptCode: string // 回单号
  receiptSend: string // 发件人
  sendTime: Date // 发件时间
  sendPhone: string // 发件人电话
  receiptRecipient: string // 接收人
  recipientTime: Date // 接收时间
  recipientPhone: string // 接收人电话
  expressCompany: string // 快递公司
  expressCode: string // 快递单号
  companyId: number // 物流公司id
}

// 回单运输信息 API
export const ReceiptTransportApi = {
  // 查询回单运输信息分页
  getReceiptTransportPage: async (params: any) => {
    return await request.get({ url: `/com/receipt-transport/page`, params })
  },

  // 查询回单运输信息详情
  getReceiptTransport: async (id: number) => {
    return await request.get({ url: `/com/receipt-transport/get?id=` + id })
  },

  // 新增回单运输信息
  createReceiptTransport: async (data: ReceiptTransportVO) => {
    return await request.post({ url: `/com/receipt-transport/create`, data })
  },

  // 修改回单运输信息
  updateReceiptTransport: async (data: ReceiptTransportVO) => {
    return await request.put({ url: `/com/receipt-transport/update`, data })
  },

  // 删除回单运输信息
  deleteReceiptTransport: async (id: number) => {
    return await request.delete({ url: `/com/receipt-transport/delete?id=` + id })
  },

  // 导出回单运输信息 Excel
  exportReceiptTransport: async (params) => {
    return await request.download({ url: `/com/receipt-transport/export-excel`, params })
  },
}

