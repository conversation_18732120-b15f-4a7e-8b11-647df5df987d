import request from '@/config/axios'

// 运单费用及科目关联 VO
export interface WaybillAccountBookVO {
  id: number // id
  accountCode: string // 科目编号
  waybillColumnName: string // 运单项列名
  waybillColumnDesc: string // 运单项描述
  waybillFeeField: string // 运单费用字段
  paidType: string // 收支类型
  paidTypeValue: string // 收支名称
  status: number // 是否有效
}

// 运单费用及科目关联 API
export const WaybillAccountBookApi = {
  // 查询运单费用及科目关联分页
  getWaybillAccountBookPage: async (params: any) => {
    return await request.get({ url: `/finance/waybill-account-book/page`, params })
  },

  // 查询运单费用及科目关联详情
  getWaybillAccountBook: async (id: number) => {
    return await request.get({ url: `/finance/waybill-account-book/get?id=` + id })
  },

  // 新增运单费用及科目关联
  createWaybillAccountBook: async (data: WaybillAccountBookVO) => {
    return await request.post({ url: `/finance/waybill-account-book/create`, data })
  },

  // 修改运单费用及科目关联
  updateWaybillAccountBook: async (data: WaybillAccountBookVO) => {
    return await request.put({ url: `/finance/waybill-account-book/update`, data })
  },

  // 删除运单费用及科目关联
  deleteWaybillAccountBook: async (id: number) => {
    return await request.delete({ url: `/finance/waybill-account-book/delete?id=` + id })
  },

  // 导出运单费用及科目关联 Excel
  exportWaybillAccountBook: async (params) => {
    return await request.download({ url: `/finance/waybill-account-book/export-excel`, params })
  },
}
