import request from '@/config/axios'

// 会计科目 VO
export interface AccountBookVO {
  id: number // 部门id
  accountCode: string // 科目编号
  accountName: string // 科目名称
  parentAccountCode: string // 上级科目
  accountLevel: number // 科目级别
  isSystem: string // 是否系统科目
  paidType: string // 科目类型
  active: string // 是否有效 
  isShow: string // 是否显示
  remark: string // 备注
  companyId: number // 公司编号
}

// 会计科目 API
export const AccountBookApi = {
  // 查询会计科目列表
  getAccountBookList: async (params) => {
    return await request.get({ url: `/finance/account-book/list`, params })
  },

  // 查询会计科目详情
  getAccountBook: async (id: number) => {
    return await request.get({ url: `/finance/account-book/get?id=` + id })
  },

  // 新增会计科目
  createAccountBook: async (data: AccountBookVO) => {
    return await request.post({ url: `/finance/account-book/create`, data })
  },

  // 修改会计科目
  updateAccountBook: async (data: AccountBookVO) => {
    return await request.put({ url: `/finance/account-book/update`, data })
  },

  // 删除会计科目
  deleteAccountBook: async (id: number) => {
    return await request.delete({ url: `/finance/account-book/delete?id=` + id })
  },

  // 导出会计科目 Excel
  exportAccountBook: async (params) => {
    return await request.download({ url: `/finance/account-book/export-excel`, params })
  },
}
