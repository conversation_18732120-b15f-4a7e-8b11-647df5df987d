import request from '@/config/axios'

// 规则 VO
export interface CodeRuleVO {
  id: number // id
  ruleName: string // 名称
  constitute: string // 构成
  connectionSymbol: string // 连接符号
  remark: string // 备注
  isSystemRule: boolean // 是否是系统默认规则
  codeDataTableId: string // 生成的码信息被记录的表格
  description: string // 规则描述
}

// 规则 API
export const CodeRuleApi = {
  // 查询规则分页
  getCodeRulePage: async (params: any) => {
    return await request.get({ url: `/bms/code-rule/page`, params })
  },

  // 查询规则详情
  getCodeRule: async (id: number) => {
    return await request.get({ url: `/bms/code-rule/get?id=` + id })
  },

  // 新增规则
  createCodeRule: async (data: CodeRuleVO) => {
    return await request.post({ url: `/bms/code-rule/create`, data })
  },

  // 预览规则详情
  previewCode: async (data: CodeRuleVO) => {
    return await request.postOriginal({ url: `/bms/code-rule/preview?` , data })
  },

  // 修改规则
  updateCodeRule: async (data: CodeRuleVO) => {
    return await request.put({ url: `/bms/code-rule/update`, data })
  },

  // 删除规则
  deleteCodeRule: async (id: number) => {
    return await request.delete({ url: `/bms/code-rule/delete?id=` + id })
  },

  // 导出规则 Excel
  exportCodeRule: async (params) => {
    return await request.download({ url: `/bms/code-rule/export-excel`, params })
  },
}
