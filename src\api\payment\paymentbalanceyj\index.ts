import request from '@/config/axios'

// 佣金结算 VO
export interface BalanceYjVO {
  id: number // id
  chargeId: string // 费用ID
  chargeCode: string // 费用代码
  chargeName: string // 费用名称
  chargeRefId: number // 运单号ID
  chargeRefNo: string // 运单号
  chargeType: string // 费用类型
  chargeTypeValue: string // 费用类型名称
  chargeAmount: number // 金额
  chargeDate: Date // 费用产生日期
  paidDeptId: number // 付款部门
  paidUser: string // 结算人员
  paidStatus: number // 结算状态
  paidType: string // 收支类型
  paidTypeValue: string // 收支类型名称
  shipperPhone: string // 发货人手机号
  auditUser: string // 审核人员
  auditTime: Date // 审核时间
  isAudit: number // 是否审核
  revokeUser: string // 反结算人员
  revokeTime: Date // 反结算时间
  isRevoke: number // 是否反结算
  shipper: string // 发货人
}

// 佣金结算 API
export const BalanceYjApi = {
  // 查询佣金结算分页
  getBalanceYjPage: async (params: any) => {
    return await request.get({ url: `/payment/balance-yj/page`, params })
  },

  // 查询佣金结算详情
  getBalanceYj: async (id: number) => {
    return await request.get({ url: `/payment/balance-yj/get?id=` + id })
  },

  // 新增佣金结算
  createBalanceYj: async (data: BalanceYjVO) => {
    return await request.post({ url: `/payment/balance-yj/create`, data })
  },

  // 修改佣金结算
  updateBalanceYj: async (data: BalanceYjVO) => {
    return await request.put({ url: `/payment/balance-yj/update`, data })
  },

  // 删除佣金结算
  deleteBalanceYj: async (id: number) => {
    return await request.delete({ url: `/payment/balance-yj/delete?id=` + id })
  },

  // 导出佣金结算 Excel
  exportBalanceYj: async (params) => {
    return await request.download({ url: `/payment/balance-yj/export-excel`, params })
  },
  // 添加佣金支付
  addPayment: async (data: BalanceYjVO) => {
    return await request.post({ url: `/payment/balance-yj/add-payment`, data })
  },
  // 取消佣金支付
  cancelPayment: async (data: BalanceYjVO) => {
    return await request.post({ url: `/payment/balance-yj/cancel-payment`, data })
  },
  // 审核佣金结算
  auditPayment: async (data: BalanceYjVO) => {
    return await request.post({ url: `/payment/balance-yj/audit-payment`, data })
  },
  //取消审核
  cancelAuditPayment: async (data: BalanceYjVO) => {
    return await request.post({ url: `/payment/balance-yj/cancel-audit-payment`, data })
  }
}
