import request from '@/config/axios'

// 网点清分方案 VO
export interface NetworkCalculatePlanVO {
  id: number // 自增序列
  logisticsNetworkId: number // 网点id
  calcType: string // 计算方式（1-比例 2-底价）
  rangeLowerBound: number // 区间下限
  rangeUpperBound: number // 区间上限
  startTime: Date // 有效期起
  endTime: Date // 有效期止
  networkValueRatio: number // 网点分成-比例
  platformValueRatio: number // 平台分成-比例
  networkValueBase: number // 网点分成-底价
  platformValueBase: number // 平台分成-底价
}

// 网点清分方案 API
export const NetworkCalculatePlanApi = {
  // 查询网点清分方案分页
  getNetworkCalculatePlanPage: async (params: any) => {
    return await request.get({ url: `/wljh/network-calculate-plan/page`, params })
  },

  // 查询网点清分方案详情
  getNetworkCalculatePlan: async (id: number) => {
    return await request.get({ url: `/wljh/network-calculate-plan/get?id=` + id })
  },

  // 新增网点清分方案
  createNetworkCalculatePlan: async (data: NetworkCalculatePlanVO) => {
    return await request.post({ url: `/wljh/network-calculate-plan/create`, data })
  },

  // 修改网点清分方案
  updateNetworkCalculatePlan: async (data: NetworkCalculatePlanVO) => {
    return await request.put({ url: `/wljh/network-calculate-plan/update`, data })
  },

  // 删除网点清分方案
  deleteNetworkCalculatePlan: async (id: number) => {
    return await request.delete({ url: `/wljh/network-calculate-plan/delete?id=` + id })
  },

  // 导出网点清分方案 Excel
  exportNetworkCalculatePlan: async (params) => {
    return await request.download({ url: `/wljh/network-calculate-plan/export-excel`, params })
  }
}
