import request from '@/config/axios'

// 回单付回款记录 VO
export interface ReceiptRecordVO {
  id: number // id
  paymentId: string // 账单ID
  paidChannel: string // 支付渠道
  receivedUser: string // 收款用户
  financeDeptId: number // 收款部门
  financeDeptName: string // 收款部门名称
  receivedAmount: number // 收款金额
  paidTime: Date // 缴款日期
  status: boolean // 收款状态 1: 正常 ；0：撤销收款
  remark: string // 备注
  revoker: string // 撤销人员
  revokeTime: Date // 撤销时间
}

// 回单付回款记录 API
export const ReceiptRecordApi = {
  // 查询回单付回款记录分页
  getReceiptRecordPage: async (params: any) => {
    return await request.get({ url: `/payment/receipt-record/page`, params })
  },

  // 查询回单付回款记录详情
  getReceiptRecord: async (id: number) => {
    return await request.get({ url: `/payment/receipt-record/get?id=` + id })
  },

  // 新增回单付回款记录
  createReceiptRecord: async (data: ReceiptRecordVO) => {
    return await request.post({ url: `/payment/receipt-record/create`, data })
  },

  // 修改回单付回款记录
  updateReceiptRecord: async (data: ReceiptRecordVO) => {
    return await request.put({ url: `/payment/receipt-record/update`, data })
  },

  // 删除回单付回款记录
  deleteReceiptRecord: async (id: number) => {
    return await request.delete({ url: `/payment/receipt-record/delete?id=` + id })
  },

  // 导出回单付回款记录 Excel
  exportReceiptRecord: async (params) => {
    return await request.download({ url: `/payment/receipt-record/export-excel`, params })
  },
  /**
   * 获取收款记录
   * @param paymentId
   */
  getReceiptRecordByPaymentId(paymentId) {
    return request.get({ url: `/payment/receipt/getReceiptRecord?paymentId=${paymentId}` })
  },
  /**
   * 撤销收款
   * @param param
   */
  revokePayment(param: { paymentId: any; remark: string; id: any }) {
    return request.delete({ url: `/payment/receipt/revokePayment`, data: param })
  }
}
