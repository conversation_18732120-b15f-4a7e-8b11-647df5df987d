import request from '@/config/axios'

// 分成方案 VO
export interface FreightProfitSchemeVO {
  id: number // 方案ID
  schemeNo: string // 方案编号
  schemeName: string // 方案名称
  billAreaId: number // 起始区域ID
  billAreaName: string // 起始区域名称
  discAreaId: number // 到达区域ID
  discAreaName: string // 到达区域名称
  effectiveTime: Date // 生效时间
  ineffectiveTime: Date // 失效时间
  companyId: number // 物流公司
  status: string // 状态
  remark: string // 备注
}

// 分成方案 API
export const FreightProfitSchemeApi = {
  // 查询分成方案分页
  getFreightProfitSchemePage: async (params: any) => {
    return await request.get({ url: `/tms/freight-profit-scheme/page`, params })
  },

  // 查询分成方案详情
  getFreightProfitScheme: async (id: number) => {
    return await request.get({ url: `/tms/freight-profit-scheme/get?id=` + id })
  },

  // 新增分成方案
  createFreightProfitScheme: async (data: FreightProfitSchemeVO) => {
    return await request.post({ url: `/tms/freight-profit-scheme/create`, data })
  },

  // 修改分成方案
  updateFreightProfitScheme: async (data: FreightProfitSchemeVO) => {
    return await request.put({ url: `/tms/freight-profit-scheme/update`, data })
  },

  // 删除分成方案
  deleteFreightProfitScheme: async (id: number) => {
    return await request.delete({ url: `/tms/freight-profit-scheme/delete?id=` + id })
  },

  // 导出分成方案 Excel
  exportFreightProfitScheme: async (params) => {
    return await request.download({ url: `/tms/freight-profit-scheme/export-excel`, params })
  },

  batchUpdate: async (data: any) => {
    return await request.put({
      url: `/tms/freight-profit-scheme/batchUpdate`,
      data
    })
  }
}

// export interface FreightProfitSchemeBatchUpdateReqVO {
//   query: {
//     schemeNo?: string
//     schemeName?: string
//     // 其他查询字段...
//     pageNo?: number
//     pageSize?: number
//   }
//   update: Partial<FreightProfitSchemeVO>
// }
