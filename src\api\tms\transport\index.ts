import request from '@/config/axios'

// 运输任务 VO
export interface TransportVO {
  id: number // 编号
  transportNo: string //运输任务编号
  loadBranchid: number // 起运部门ID
  loadBranchname: string
  discBranchid: number // 到达部门ID
  backBranchid: number // 返程部门ID
  vehicleLicenseNumber: string // 车牌号
  driverId: number // 司机ID
  transportType: number // 运输类型
  transportStatus: number // 运输状态
  backVoyage: number // 是否往返
  autoVoyage: number // 是否自动创建车次
  loadType: string // 装车类型
  remark: string // 备注
  totalAmount: number // 总车费
  prepayAmount: number // 预付车费
  arrivedAmount: number // 到付车费
  remainAmount: number // 回付车费
}

// 运输任务 API
export const TransportApi = {
  // 查询运输任务分页
  getTransportPage: async (params: any) => {
    return await request.get({ url: `/tms/transport/page`, params })
  },

  //
  getTransportList: async (loadBranchid: number, discBranchid: number ) => {
    return await request.get({ url: `/tms/transport/list`, params: { loadBranchid, discBranchid} });
  },

  getTransports: async (loadBranchid: number, discBranchid: number ) => {
    return await request.get({ url: `/tms/transport/list-transport`, params: { loadBranchid, discBranchid} });
  },
  

  // 查询运输任务详情
  getTransport: async (id: number) => {
    return await request.get({ url: `/tms/transport/get?id=` + id })
  },

  // 新增运输任务
  createTransport: async (data: TransportVO) => {
    return await request.post({ url: `/tms/transport/create`, data })
  },

  // 修改运输任务
  updateTransport: async (data: TransportVO) => {
    return await request.put({ url: `/tms/transport/update`, data })
  },

  // 删除运输任务
  deleteTransport: async (id: number) => {
    return await request.delete({ url: `/tms/transport/delete?id=` + id })
  },

  // 导出运输任务 Excel
  exportTransport: async (params) => {
    return await request.download({ url: `/tms/transport/export-excel`, params })
  },

// ==================== 子表（运输车次） ====================

  // 获得运输车次列表
  getVoyageListByTransportId: async (transportId) => {
    return await request.get({ url: `/tms/transport/voyage/list-by-transport-id?transportId=` + transportId })
  },

  // ==================== 子表（运输车费） ====================

  // 获得运输车费列表
  // getTransportChargeListByTransportId: async (transportId) => {
  //   return await request.get({ url: `/tms/transport/transport-charge/list-by-transport-id?transportId=` + transportId })
  // },

  // ==================== 子表（运输任务途经站点） ====================

  // 获得运输任务途经站点列表
  getTransportDtlListByTransportNo: async (trransportNo) => {
    return await request.get({ url: `/tms/transport/transport-dtl/list-by-transport-no?trransportNo=` + trransportNo })
  },
}
