import request from '@/config/axios'

// 车队 VO
export interface MotorcadeVO {
  id: number // 编号
  motorcadeName: string // 车队名称
  motorcadeAgency: string // 所属机构
  motorcadeHead: string // 车队负责人
  headMobile: string // 负责人电话
  remark: string // 备注
}

// 车队 API
export const MotorcadeApi = {
  // 查询车队分页
  getMotorcadePage: async (params: any) => {
    return await request.get({ url: `/vms/motorcade/page`, params })
  },

  // 查询车队详情
  getMotorcade: async (id: number) => {
    return await request.get({ url: `/vms/motorcade/get?id=` + id })
  },

  // 新增车队
  createMotorcade: async (data: MotorcadeVO) => {
    return await request.post({ url: `/vms/motorcade/create`, data })
  },

  // 修改车队
  updateMotorcade: async (data: MotorcadeVO) => {
    return await request.put({ url: `/vms/motorcade/update`, data })
  },

  // 删除车队
  deleteMotorcade: async (id: number) => {
    return await request.delete({ url: `/vms/motorcade/delete?id=` + id })
  },

  // 导出车队 Excel
  exportMotorcade: async (params) => {
    return await request.download({ url: `/vms/motorcade/export-excel`, params })
  },

// ==================== 子表（车辆） ====================

  // 获得车辆分页
  getVehiclesPage: async (params) => {
    return await request.get({ url: `/vms/motorcade/vehicles/page`, params })
  },
  // 新增车辆
  createVehicles: async (data) => {
    return await request.post({ url: `/vms/motorcade/vehicles/create`, data })
  },

  // 修改车辆
  updateVehicles: async (data) => {
    return await request.put({ url: `/vms/motorcade/vehicles/update`, data })
  },

  // 删除车辆
  deleteVehicles: async (id: number) => {
    return await request.delete({ url: `/vms/motorcade/vehicles/delete?id=` + id })
  },

  // 获得车辆
  getVehicles: async (id: number) => {
    return await request.get({ url: `/vms/motorcade/vehicles/get?id=` + id })
  },


  getVehiclesList: async (motorcadeId: number) => {
    return await request.get({ url: `/vms/motorcade/vehicles/getVehiclesList?motorcadeId=` + motorcadeId })
  },

  //根据车牌号获取车辆
  getVehiclesByVehicleLicenseNumber: async (vehicleLicenseNumber: string) => {
    return await request.get({ url: `/vms/motorcade/vehicles/getVehiclesByVehicleLicenseNumber?vehicleLicenseNumber=` + vehicleLicenseNumber })
  },

// ==================== 子表（司机） ====================

  // 获得司机分页
  getDriverPage: async (params) => {
    return await request.get({ url: `/vms/motorcade/driver/page`, params })
  },
  // 新增司机
  createDriver: async (data) => {
    return await request.post({ url: `/vms/motorcade/driver/create`, data })
  },

  // 修改司机
  updateDriver: async (data) => {
    return await request.put({ url: `/vms/motorcade/driver/update`, data })
  },

  // 删除司机
  deleteDriver: async (id: number) => {
    return await request.delete({ url: `/vms/motorcade/driver/delete?id=` + id })
  },

  // 获得司机
  getDriver: async (id: number) => {
    return await request.get({ url: `/vms/motorcade/driver/get?id=` + id })
  },

    // 获得司机
  getDriverList: async (motorcadeId: number) => {
    return await request.get({ url: `/vms/motorcade/driver/getDriverList?motorcadeId=` + motorcadeId })
  },
}
