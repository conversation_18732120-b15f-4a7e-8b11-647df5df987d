import request from '@/config/axios'

// 回单付收支信息 VO
export interface MonthPendingVO {
  id: number // id
  chargeId: string // 费用ID
  paymentId: string // 账单ID
  chargeCode: string // 费用代码
  chargeName: string // 费用名称
  chargeRefId: number // 运单号ID
  chargeRefNo: string // 运单号
  chargeType: string // 费用类型
  chargeTypeValue: string // 费用类型名称；例如：主营业务等
  chargeAmount: number // 金额
  chargeDate: Date // 费用产生日期
  receDeptId: number // 收款部门ID
  receDeptName: string // 收款部门名称
  chargeStatus: boolean // 回款状态 1：已回款 0：未回款
  shipper: string // 发货人
  shipperMobile: string // 发货人电话
  itemDesc: string // 物品描述
  deptId: number // 部门ID
  deptName: string // 部门名称

}

// 回单付收支信息 API
export const MonthPendingApi = {
  // 查询回单付收支信息分页
  getMonthPendingPage: async (params: any) => {
    return await request.get({ url: `/payment/month-pending/page`, params })
  },

  // 查询回单付收支信息详情
  getMonthPending: async (id: number) => {
    return await request.get({ url: `/payment/month-pending/get?id=` + id })
  },

  // 新增回单付收支信息
  createMonthPending: async (data: MonthPendingVO) => {
    return await request.post({ url: `/payment/month-pending/create`, data })
  },

  // 修改回单付收支信息
  updateMonthPending: async (data: MonthPendingVO) => {
    return await request.put({ url: `/payment/month-pending/update`, data })
  },

  // 删除回单付收支信息
  deleteMonthPending: async (id: number) => {
    return await request.delete({ url: `/payment/month-pending/delete?id=` + id })
  },

  // 导出回单付收支信息 Excel
  exportMonthPending: async (params) => {
    return await request.download({ url: `/payment/month-pending/export-excel`, params })
  },
  // 查询回单付列表
  getMonthPendingList: async (params: any) => {
    return await request.get({ url: `/payment/month-pending/list`, params })
  },
  // 根据账单号查询回单付列表
  getMonthPendingListByPaymentId: async (params: any) => {
    return await request.get({ url: `/payment/month-pending/list-by-payment-id`, params })
  },
  // 查询没有账单号的回单付列表
  getMonthPendingListNoPaymentId: async (params: any) => {
    return await request.get({ url: `/payment/month-pending/list-without-payment-id`, params })
  }
}
