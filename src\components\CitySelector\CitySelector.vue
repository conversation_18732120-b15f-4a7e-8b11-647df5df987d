<template>
  <el-form-item :label="label" :prop="propName">
    <el-cascader v-model="selectedArea" :options="areaList" :props="defaultProps" class="w-full" clearable filterable
      :placeholder="$t('请选择省市区地址')" @change="handleCityChange" />
  </el-form-item>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import * as AreaApi from '@/api/system/area';

// 假设 areaList 是从后端获取的数据
const areaList = ref([]);
const selectedArea = ref([]);

// 定义组件接收的外部属性
const props = defineProps({
  propName: {
    type: String,
    required: true
  },
  modelValue: {
    type: Array,
    default: () => []
  },
  label: {
    type: String,
    default: '地区选择'
  }
});

// 默认的级联选择器属性配置
const defaultProps = {
  children: 'children',
  label: 'name',
  value: 'id',
  isLeaf: 'leaf',
  emitPath: true // 不返回路径，只返回当前节点的值
};

const emit = defineEmits(['update:modelValue']);

// 新增工具函数：根据行政编码结构查找路径
const findPathByCode = (targetCode) => {
  if (!targetCode || targetCode.toString().length !== 6) return null
  
  const codeStr = targetCode.toString().padStart(6, '0')
  
  // 生成各级行政编码
  const provinceCode = codeStr.substring(0, 2) + '0000' // 省级编码
  const cityCode = codeStr.substring(0, 4) + '00'      // 市级编码
  const districtCode = codeStr                        // 区级编码
  
  // 转换为数字类型（根据实际数据结构决定）
  const provinceId = parseInt(provinceCode)
  const cityId = parseInt(cityCode)
  const districtId = parseInt(districtCode)
  
  // 解除响应式包装
  const rawData = toRaw(areaList.value)
  
  // 逐级查找
  const province = rawData.find(p => p.id === provinceId)
  if (!province) return null
  
  const city = province.children?.find(c => c.id === cityId)
  if (!city) return [provinceId] // 只到省级
  
  const district = city.children?.find(d => d.id === districtId)
  if (!district) return [provinceId, cityId] // 只到市级
  
  return [provinceId, cityId, districtId]
}

// 修改后的loadSelectedValue
const loadSelectedValue = async () => {
  if (!props.modelValue) return
  
  // 等待数据加载
  if (!areaList.value.length) {
    areaList.value = await AreaApi.getAreaTree()
  }
  
  const path = findPathByCode(props.modelValue)
  
  if (path) {
    selectedArea.value = path
  } else {
    console.error('路径解析失败，请检查：',
      `1. 编码 ${props.modelValue} 是否符合规范`,
      '2. 数据是否包含完整的三级结构',
      '3. 控制台输出的原始数据:', JSON.parse(JSON.stringify(areaList.value))
    )
  }
}

// 新增监听器
watch(() => props.modelValue, loadSelectedValue, { immediate: true })

// 在组件挂载完成后获取地区列表
onMounted(async () => {
  try {
    areaList.value = await AreaApi.getAreaTree();
  } catch (error) {
    console.error('Failed to fetch area list:', error);
  }
});
// 修改change处理（保持与v-model同步）
const handleCityChange = (path) => {
  const lastId = path?.[path.length - 1]
  emit('update:modelValue', lastId)
};
</script>
<!--   
  <style scoped>
  .w-full {
    width: 100%;
  }
  </style> -->