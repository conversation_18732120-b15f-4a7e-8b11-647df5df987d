import request from '@/config/axios'

// 生成码 VO
export interface CodeDataVO {
  id: number // id
  code: string // 码
  status: boolean // 状态
  appleId: number // 申请id
  remark: string // 描述
  deleteTime: Date // 删除时间
}

// 生成码 API
export const CodeDataApi = {
  // 查询生成码分页
  getCodeDataPage: async (params: any) => {
    return await request.get({ url: `/bms/code-data/page`, params })
  },

  // 查询生成码详情
  getCodeData: async (id: number) => {
    return await request.get({ url: `/bms/code-data/get?id=` + id })
  },


  // 预览生成码
  previewQrCode: async (data: CodeDataVO) => {
    return await request.postOriginal({ url: `/bms/code-data/code-image`, data })
  },

  // 新增生成码
  createCodeData: async (data: CodeDataVO) => {
    return await request.post({ url: `/bms/code-data/create`, data })
  },

  // 修改生成码
  updateCodeData: async (data: CodeDataVO) => {
    return await request.put({ url: `/bms/code-data/update`, data })
  },

  // 删除生成码
  deleteCodeData: async (id: number) => {
    return await request.delete({ url: `/bms/code-data/delete?id=` + id })
  },

  // 导出生成码 Excel
  exportCodeData: async (params) => {
    return await request.download({ url: `/bms/code-data/export-excel`, params })
  },
}
