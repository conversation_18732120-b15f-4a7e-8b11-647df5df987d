import request from '@/config/axios'

// 运单中转 VO
export interface WaybillTransferVO {
  id: number // 编号
  transferNos: string // 转运编号
  waybillId: number // 运单ID
  waybillNo: string // 运单号
  carrierWaybill: string // 转运公司运单编号
  carrierId: number // 承运商ID
  carrierName: string //承运商名称
  loadBranchid: number //承运商发货站ID
  loadBranchName: string //承运商发货站名称
  discBranchid: number //承运商到货站ID
  discBranchName: string // 承运商到货站名称
  transferBatch: string // 转货批次编号
  transferTime: Date // 中转时间
  remark: string // 备注
}

// 运单中转 API
export const WaybillTransferApi = {
  // 查询运单中转分页
  getWaybillTransferPage: async (params: any) => {
    return await request.get({ url: `/tms/waybill-transfer/page`, params })
  },

  // 查询运单中转详情
  getWaybillTransfer: async (id: number) => {
    return await request.get({ url: `/tms/waybill-transfer/get?id=` + id })
  },

  // 新增运单中转
  createWaybillTransfer: async (data: WaybillTransferVO) => {
    return await request.post({ url: `/tms/waybill-transfer/create`, data })
  },

  // 修改运单中转
  updateWaybillTransfer: async (data: WaybillTransferVO) => {
    return await request.put({ url: `/tms/waybill-transfer/update`, data })
  },

  // 删除运单中转
  deleteWaybillTransfer: async (id: number) => {
    return await request.delete({ url: `/tms/waybill-transfer/delete?id=` + id })
  },

  // 导出运单中转 Excel
  exportWaybillTransfer: async (params) => {
    return await request.download({ url: `/tms/waybill-transfer/export-excel`, params })
  },
}
