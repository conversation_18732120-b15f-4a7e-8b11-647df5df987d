import request from '@/config/axios'

// 车辆 VO
export interface VehiclesVO {
  id: number // 车辆编号
  vehicleLicenseNumber: string // 车牌号码
  vehicleLicenseColor: string // 车牌颜色
  vehicleType: string // 车辆类型
  usePurpose: string // 使用性质
  vehicleMark: string // 车辆标记
  energyType: string // 能源类型
  exhaustEmissionStandard: string // 排放标准
  remark: string // 备注
}

// 车辆 API
export const VehiclesApi = {
  // 查询车辆分页
  getVehiclesPage: async (params: any) => {
    return await request.get({ url: `/vms/vehicles/page`, params })
  },

  // 查询车辆详情
  getVehicles: async (id: number) => {
    return await request.get({ url: `/vms/vehicles/get?id=` + id })
  },

  // 获取车辆列表
  getVehicleList: async () => {
    return await request.get({ url: `/vms/vehicles/get-list`})
  },

  // 新增车辆
  createVehicles: async (data: VehiclesVO) => {
    return await request.post({ url: `/vms/vehicles/create`, data })
  },

  // 修改车辆
  updateVehicles: async (data: VehiclesVO) => {
    return await request.put({ url: `/vms/vehicles/update`, data })
  },

  // 删除车辆
  deleteVehicles: async (id: number) => {
    return await request.delete({ url: `/vms/vehicles/delete?id=` + id })
  },

  // 导出车辆 Excel
  exportVehicles: async (params) => {
    return await request.download({ url: `/vms/vehicles/export-excel`, params })
  },

// ==================== 子表（车辆证件） ====================

  // 获得车辆证件分页
  getVehiclesCredentialPage: async (params) => {
    return await request.get({ url: `/vms/vehicles/vehicles-credential/page`, params })
  },
  // 新增车辆证件
  createVehiclesCredential: async (data) => {
    return await request.post({ url: `/vms/vehicles/vehicles-credential/create`, data })
  },

  // 修改车辆证件
  updateVehiclesCredential: async (data) => {
    return await request.put({ url: `/vms/vehicles/vehicles-credential/update`, data })
  },

  // 删除车辆证件
  deleteVehiclesCredential: async (id: number) => {
    return await request.delete({ url: `/vms/vehicles/vehicles-credential/delete?id=` + id })
  },

  // 获得车辆证件
  getVehiclesCredential: async (id: number) => {
    return await request.get({ url: `/vms/vehicles/vehicles-credential/get?id=` + id })
  },

// ==================== 子表（车辆保险年检） ====================

  // 获得车辆保险年检分页
  getInsurancePage: async (params) => {
    return await request.get({ url: `/vms/vehicles/insurance/page`, params })
  },
  // 新增车辆保险年检
  createInsurance: async (data) => {
    return await request.post({ url: `/vms/vehicles/insurance/create`, data })
  },

  // 修改车辆保险年检
  updateInsurance: async (data) => {
    return await request.put({ url: `/vms/vehicles/insurance/update`, data })
  },

  // 删除车辆保险年检
  deleteInsurance: async (id: number) => {
    return await request.delete({ url: `/vms/vehicles/insurance/delete?id=` + id })
  },

  // 获得车辆保险年检
  getInsurance: async (id: number) => {
    return await request.get({ url: `/vms/vehicles/insurance/get?id=` + id })
  },

  // ==================== 子表（车辆事故记录） ====================

  // 获得车辆事故记录分页
  getVehiclesAccidentPage: async (params) => {
    return await request.get({ url: `/vms/vehicles/vehicles-accident/page`, params })
  },
  // 新增车辆事故记录
  createVehiclesAccident: async (data) => {
    return await request.post({ url: `/vms/vehicles/vehicles-accident/create`, data })
  },

  // 修改车辆事故记录
  updateVehiclesAccident: async (data) => {
    return await request.put({ url: `/vms/vehicles/vehicles-accident/update`, data })
  },

  // 删除车辆事故记录
  deleteVehiclesAccident: async (id: number) => {
    return await request.delete({ url: `/vms/vehicles/vehicles-accident/delete?id=` + id })
  },

  // 获得车辆事故记录
  getVehiclesAccident: async (id: number) => {
    return await request.get({ url: `/vms/vehicles/vehicles-accident/get?id=` + id })
  },

// ==================== 子表（车辆能源补给） ====================

  // 获得车辆能源补给分页
  getVehiclesEnergyPage: async (params) => {
    return await request.get({ url: `/vms/vehicles/vehicles-energy/page`, params })
  },
  // 新增车辆能源补给
  createVehiclesEnergy: async (data) => {
    return await request.post({ url: `/vms/vehicles/vehicles-energy/create`, data })
  },

  // 修改车辆能源补给
  updateVehiclesEnergy: async (data) => {
    return await request.put({ url: `/vms/vehicles/vehicles-energy/update`, data })
  },

  // 删除车辆能源补给
  deleteVehiclesEnergy: async (id: number) => {
    return await request.delete({ url: `/vms/vehicles/vehicles-energy/delete?id=` + id })
  },

  // 获得车辆能源补给
  getVehiclesEnergy: async (id: number) => {
    return await request.get({ url: `/vms/vehicles/vehicles-energy/get?id=` + id })
  },

// ==================== 子表（车辆年检） ====================

  // 获得车辆年检分页
  getVehiclesInspectionPage: async (params) => {
    return await request.get({ url: `/vms/vehicles/vehicles-inspection/page`, params })
  },
  // 新增车辆年检
  createVehiclesInspection: async (data) => {
    return await request.post({ url: `/vms/vehicles/vehicles-inspection/create`, data })
  },

  // 修改车辆年检
  updateVehiclesInspection: async (data) => {
    return await request.put({ url: `/vms/vehicles/vehicles-inspection/update`, data })
  },

  // 删除车辆年检
  deleteVehiclesInspection: async (id: number) => {
    return await request.delete({ url: `/vms/vehicles/vehicles-inspection/delete?id=` + id })
  },

  // 获得车辆年检
  getVehiclesInspection: async (id: number) => {
    return await request.get({ url: `/vms/vehicles/vehicles-inspection/get?id=` + id })
  },

// ==================== 子表（车辆保险） ====================

  // 获得车辆保险分页
  getVehiclesInsurancePage: async (params) => {
    return await request.get({ url: `/vms/vehicles/vehicles-insurance/page`, params })
  },
  // 新增车辆保险
  createVehiclesInsurance: async (data) => {
    return await request.post({ url: `/vms/vehicles/vehicles-insurance/create`, data })
  },

  // 修改车辆保险
  updateVehiclesInsurance: async (data) => {
    return await request.put({ url: `/vms/vehicles/vehicles-insurance/update`, data })
  },

  // 删除车辆保险
  deleteVehiclesInsurance: async (id: number) => {
    return await request.delete({ url: `/vms/vehicles/vehicles-insurance/delete?id=` + id })
  },

  // 获得车辆保险
  getVehiclesInsurance: async (id: number) => {
    return await request.get({ url: `/vms/vehicles/vehicles-insurance/get?id=` + id })
  },

// ==================== 子表（车辆保养记录） ====================

  // 获得车辆保养记录分页
  getVehiclesMaintenancePage: async (params) => {
    return await request.get({ url: `/vms/vehicles/vehicles-maintenance/page`, params })
  },
  // 新增车辆保养记录
  createVehiclesMaintenance: async (data) => {
    return await request.post({ url: `/vms/vehicles/vehicles-maintenance/create`, data })
  },

  // 修改车辆保养记录
  updateVehiclesMaintenance: async (data) => {
    return await request.put({ url: `/vms/vehicles/vehicles-maintenance/update`, data })
  },

  // 删除车辆保养记录
  deleteVehiclesMaintenance: async (id: number) => {
    return await request.delete({ url: `/vms/vehicles/vehicles-maintenance/delete?id=` + id })
  },

  // 获得车辆保养记录
  getVehiclesMaintenance: async (id: number) => {
    return await request.get({ url: `/vms/vehicles/vehicles-maintenance/get?id=` + id })
  },

// ==================== 子表（车辆维修记录） ====================

  // 获得车辆维修记录分页
  getVehiclesRepairPage: async (params) => {
    return await request.get({ url: `/vms/vehicles/vehicles-repair/page`, params })
  },
  // 新增车辆维修记录
  createVehiclesRepair: async (data) => {
    return await request.post({ url: `/vms/vehicles/vehicles-repair/create`, data })
  },

  // 修改车辆维修记录
  updateVehiclesRepair: async (data) => {
    return await request.put({ url: `/vms/vehicles/vehicles-repair/update`, data })
  },

  // 删除车辆维修记录
  deleteVehiclesRepair: async (id: number) => {
    return await request.delete({ url: `/vms/vehicles/vehicles-repair/delete?id=` + id })
  },

  // 获得车辆维修记录
  getVehiclesRepair: async (id: number) => {
    return await request.get({ url: `/vms/vehicles/vehicles-repair/get?id=` + id })
  },

// ==================== 子表（车辆轮胎管理） ====================

  // 获得车辆轮胎管理分页
  getVehiclesTirePage: async (params) => {
    return await request.get({ url: `/vms/vehicles/vehicles-tire/page`, params })
  },
  // 新增车辆轮胎管理
  createVehiclesTire: async (data) => {
    return await request.post({ url: `/vms/vehicles/vehicles-tire/create`, data })
  },

  // 修改车辆轮胎管理
  updateVehiclesTire: async (data) => {
    return await request.put({ url: `/vms/vehicles/vehicles-tire/update`, data })
  },

  // 删除车辆轮胎管理
  deleteVehiclesTire: async (id: number) => {
    return await request.delete({ url: `/vms/vehicles/vehicles-tire/delete?id=` + id })
  },

  // 获得车辆轮胎管理
  getVehiclesTire: async (id: number) => {
    return await request.get({ url: `/vms/vehicles/vehicles-tire/get?id=` + id })
  }
}
