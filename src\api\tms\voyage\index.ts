import request from '@/config/axios'

// 运输车次 VO
export interface VoyageVO {
  id: number //车次ID
  transportId: number // 运输任务ID
  transportNo: string // 运输任务编号
  departureTime: Date // 发车日期
  closeLabelno: string // 封签号
  voyageNo: string //车次编号
  voyageStatus: number //车次状态
  voyageType: number //运输状态
  loadBranchid: number // 起始部门ID
  loadBranchname: string // 起始部门名称
  discBranchid: number // 到达部门ID
  discBranchname: string // 到达部门名称
  vehicleId: number //车辆ID
  vehicleLicenseNumber: string // 车牌号
  vehicleType: number // 车辆类型
  driverId: number // 司机ID
  driverName: string // 司机姓名
  contactMobile: string // 司机手机号
  driverId2: number // 司机ID
  driverName2: string // 司机姓名
  contactMobile2: string // 司机手机号
  contractNum: string // 车次合同号
  expectarriveTime: Date // 预计到达时间
  sealStatus: number //封车状态
  lateReason: string // 晚点原因
  remark: string // 备注
  totalQty: number //总件数
  totalKgs: number //总重量
  totalCbm: number //总体积
  totalFreight: number //总运费（每单运费相加）
  totalOrder: number //总单数
  totalAmount: number //总车费
  prepayAmount: number // 预付车费
  arrivedAmount: number // 到付车费
  remainAmount: number // 回付车费
}

// 运输车次 API
export const VoyageApi = {
  // 查询运输车次分页
  getVoyagePage: async (params: any) => {
    return await request.get({ url: `/tms/voyage/page`, params })
  },

  //
  // getTransportList: async (params: { loadBranchid: number, discBranchid: number }) => {
  //   return await request.get({ url: `/tms/transport/list`, params });
  // },

  // 查询运输车次详情
  getVoyage: async (id: number) => {
    return await request.get({ url: `/tms/voyage/get?id=` + id })
  },

  // 查询运输车次详情
  getVoyageByVoyageNo: async (voyageNo: String) => {
    return await request.get({ url: `/tms/voyage/get-voyage-no?voyageNo=` + voyageNo })
  },

  // 新增运输车次
  createVoyage: async (data: VoyageVO) => {
    return await request.post({ url: `/tms/voyage/create`, data })
  },

  // 修改运输车次
  updateVoyage: async (data: VoyageVO) => {
    return await request.put({ url: `/tms/voyage/update`, data })
  },

  // 修改运输车次状态
  updateVoyageStatus: async (data: VoyageVO) => {
    return await request.put({ url: `/tms/voyage/update-status`, data })
  },

  // 删除运输车次
  deleteVoyage: async (id: number) => {
    return await request.delete({ url: `/tms/voyage/delete?id=` + id })
  },

  // 导出运输车次 Excel
  exportVoyage: async (params) => {
    return await request.download({ url: `/tms/voyage/export-excel`, params })
  },

  // ==================== 子表（车次订单） ====================

  // 获得车次订单列表
  getVoyageOrderListByVoyageId: async (voyageId) => {
    return await request.get({ url: `/tms/voyage/voyage-order/list-by-voyage-id?voyageId=` + voyageId })
  },

  createVoyageOrder :async (voyageId, voyageNo, orderbarNos) => {
  return await request.post({ url: `/tms/voyage/voyage-order/create`,  data:{ voyageId, voyageNo , orderbarNos}});
},

  // 查询车次车费信息
  getVoyageChargeList: async (params: any) => {
    console.log(params)
    return await request.get({ url: `/tms/voyage/voyage-charge`, params })
  },
}
