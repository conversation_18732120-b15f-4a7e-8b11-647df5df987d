<template>
  <el-form-item :label="label" :prop="propName">
    <el-tree-select
      v-model="selectedDeptId"
      :data="deptList"
      :render-after-expand="false"
      class="!w-240px"
      check-strictly
      :props="treeProps"
      :placeholder="placeholder"
      :disabled="isQueryCondition"
      @change="handleDeptChange"
      clearable
    />
  </el-form-item>
</template>
  
  <script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import * as DeptApi from '@/api/system/dept'

// 定义选项列表
const deptList = ref([])

// 定义组件接收的外部属性
const props = defineProps({
  propName: {
    type: String,
    required: true
  },
  modelValue: {
    type: [String, Number],
    default: null
  },
  label: {
    type: String,
    default: '所属部门'
  },
  placeholder: {
    type: String,
    default: '请选择所属部门'
  },
  isQueryCondition: {
    type: Boolean,
    default: false // 是否作为查询条件，默认为false
  },
  deptTypes: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue'])

// 构建树形结构
const buildTree = (flatData, parentId) => {
  return flatData
    .filter((item) => item.parentId === parentId)
    .map((item) => {
      const children = buildTree(flatData, item.id)
      return {
        ...item,
        children,
        isLeaf: children.length === 0
      }
    })
}

const queryParams = reactive({
  pageNo: 1,
  pageSize: 600,
  name: undefined,
  status: 0,
  deptTypes: []
})

// 获取部门列表
const getDeptList = async () => {
  try {
    const data = await DeptApi.getSimpleDeptList(queryParams)

    // console.log('原始数据:', data)

    // 将扁平数据转换成树形结构
    const rootNodes = buildTree(data, 0)
    deptList.value = rootNodes
    deptList.value.unshift({
      name: '取消选择',
      id: undefined
    })
    // console.log('树形数据:', deptList.value)
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败，请稍后再试！')
    deptList.value = []
  }
}

// 在组件挂载完成后获取部门列表
onMounted(() => {
  getDeptList()
})

// 监听 deptTypes 的变化
watch(
  () => props.deptTypes,
  (newDeptTypes) => {
    if (newDeptTypes.length > 0) {
      queryParams.deptTypes = newDeptTypes
      getDeptList()
    }
  },
  { immediate: true } // 立即执行一次
)

const selectedDeptId = ref(props.modelValue || null) // 初始值根据modelValue设置

// 使用watch来监听modelValue的变化，并更新selectedDeptId
watch(
  () => props.modelValue,
  (newVal) => {
    selectedDeptId.value = newVal // 当modelValue变化时更新selectedDeptId
  }
)

// 处理选中变化
const handleDeptChange = (value) => {
  if (!props.isQueryCondition) {
    emit('update:modelValue', value)
  }
}

// 定义树形选择器的属性
const treeProps = {
  label: 'name',
  children: 'children',
  value: 'id',
  isLeaf: 'isLeaf'
}
</script>
  
  
  
  
  
  

  