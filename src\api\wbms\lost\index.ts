import request from '@/config/axios'

// 运单挂失申请 VO
export interface LostVO {
  id: number // id
  waybillNo: string // 运单号
  waybillStatus: string // 运单状态
  lostStatus: string // 挂失状态
  lostType: string // 挂失类型
  lostApplyPic: string // 挂失申请人
  lostApplyIdcard: string // 挂失人证件号
  lostAmount: number // 挂失费
  lostReason: string // 挂失原因
  isAutoRls: number // 是否自动解挂
  autoRlsTime: Date // 自动解挂时间
  lostUserId: number // 挂失人编号
  lostUserName: string // 挂失人名称
  lostDeptId: number // 挂失部门编号
  lostDeptName: string // 挂失部门名称
  lostTime: Date // 挂失时间
  rlsType: string // 解挂类型
  rlsApplyPic: string // 解挂申请人
  rlsApplyIdcard: string // 解挂人证件号
  rlsRemark: string // 解挂备注
  rlsUserId: number // 解挂人编号
  rlsUserName: string // 解挂人名称
  rlsDeptId: number // 解挂部门编号
  rlsDeptName: string // 解挂部门名称
  rlsTime: Date // 解挂时间
}

// 运单挂失申请 API
export const LostApi = {
  // 查询运单挂失申请分页
  getLostPage: async (params: any) => {
    return await request.get({ url: `/wbms/lost/page`, params })
  },

  // 查询运单挂失申请详情
  getLost: async (id: number) => {
    return await request.get({ url: `/wbms/lost/get?id=` + id })
  },

  // 新增运单挂失申请
  createLost: async (data: LostVO) => {
    return await request.post({ url: `/wbms/lost/create`, data })
  },

  // 修改运单挂失申请
  updateLost: async (data: LostVO) => {
    return await request.put({ url: `/wbms/lost/update`, data })
  },

  // 删除运单挂失申请
  deleteLost: async (id: number) => {
    return await request.delete({ url: `/wbms/lost/delete?id=` + id })
  },

  // 导出运单挂失申请 Excel
  exportLost: async (params) => {
    return await request.download({ url: `/wbms/lost/export-excel`, params })
  },
}
