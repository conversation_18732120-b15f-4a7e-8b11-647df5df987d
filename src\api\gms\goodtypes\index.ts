import request from '@/config/axios'

// 货品分类 VO
export interface GoodTypesVO {
  id: number // 编号
  name: string // 名字
  parentId: number // 父级编号
  classCode: string // 分类代码
  remark: string // 备注
}

// 货品分类 API
export const GoodTypesApi = {
  // 查询货品分类列表
  getGoodTypesList: async (params) => {
    return await request.get({ url: `/gms/good-types/list`, params })
  },

  // 查询货品分类详情
  getGoodTypes: async (id: number) => {
    return await request.get({ url: `/gms/good-types/get?id=` + id })
  },

  // 新增货品分类
  createGoodTypes: async (data: GoodTypesVO) => {
    return await request.post({ url: `/gms/good-types/create`, data })
  },

  // 修改货品分类
  updateGoodTypes: async (data: GoodTypesVO) => {
    return await request.put({ url: `/gms/good-types/update`, data })
  },

  // 删除货品分类
  deleteGoodTypes: async (id: number) => {
    return await request.delete({ url: `/gms/good-types/delete?id=` + id })
  },

  // 导出货品分类 Excel
  exportGoodTypes: async (params) => {
    return await request.download({ url: `/gms/good-types/export-excel`, params })
  },
}
