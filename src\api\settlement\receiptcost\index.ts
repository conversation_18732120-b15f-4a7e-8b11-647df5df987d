import request from '@/config/axios'

// 回款单 VO
export interface ReceiptCostVO {
  id: number // id
  userId: number // 用户id
  deptId: number // 部门id
  receiptNo: string // 回款单
  receptTime: Date // 回款时间
  receptDept: number // 回款部门
  collectDept: number // 收款部门
  audit: boolean // 审核状态
  dryh: number // 当日应回
  drfk: number // 当日付款
  drqk: number // 当日欠款
  zrljqk: number // 昨日累计欠款
  ljqk: number // 累计欠款
  fyhj: number // 费用合计
  srhj: number // 收入合计
  zchj: number // 支出合计
  tfsr: number // 提付收入
  xfsr: number // 现付收入
  tfyjsr: number // 提付月结收入
  xfyjsr: number // 现付月结收入
  hdfsr: number // 回单付收入
  kfsr: number // 扣付收入
  dsksr: number // 代收款收入
  rcfysr: number // 日常费用收入
  yjzc: number // 佣金支出
  zzfzc: number // 中转费支出
  rcfyzc: number // 日常费用支出
  shfzc: number // 送货费支出
  settlementUser: number // 结算人
  settlementTime: Date // 结算时间
}

// 回款单 API
export const ReceiptCostApi = {
  // 查询回款单分页
  getReceiptCostPage: async (params: any) => {
    return await request.get({ url: `/settlement/receipt-cost/page`, params })
  },

  // 查询回款单详情
  getReceiptCost: async (id: number) => {
    return await request.get({ url: `/settlement/receipt-cost/get?id=` + id })
  },

  // 新增回款单
  createReceiptCost: async (data: ReceiptCostVO) => {
    return await request.post({ url: `/settlement/receipt-cost/create`, data })
  },

  // 修改回款单
  updateReceiptCost: async (data: ReceiptCostVO) => {
    return await request.put({ url: `/settlement/receipt-cost/update`, data })
  },

  // 删除回款单
  deleteReceiptCost: async (id: number) => {
    return await request.delete({ url: `/settlement/receipt-cost/delete?id=` + id })
  },

  // 导出回款单 Excel
  exportReceiptCost: async (params) => {
    return await request.download({ url: `/settlement/receipt-cost/export-excel`, params })
  },
}
