import { useTagsView } from '@/hooks/web/useTagsView'
import { useTagsViewStore } from '@/store/modules/tagsView'
import { useRouter } from 'vue-router'

// 关闭页面钩子函数，关闭当前页面，并跳转到最新页面
export const useCloseCurrentPage = () => {
  const { closeCurrent } = useTagsView()
  const tagsViewStore = useTagsViewStore()
  const { push } = useRouter()

  const closeCurrentPage = () => {
    closeCurrent()
    const visitedViews = tagsViewStore.getVisitedViews
    const latestView = visitedViews.slice(-1)[0]
    if (latestView) {
      latestView.query = { t: Date.now() + '' }
      push(latestView)
    }
  }

  return {
    closeCurrentPage
  }
}