import request from '@/config/axios'

// 物流公司划转记录 VO
export interface CompanyTransferLogVO {
  id: number // 自增序列
  logisticsCompaniesId: number // 物流公司id
  companyFreightValue: number // 物流公司运费账户增加金额
  companyTaxValue: number // 物流公司税费账户增加金额
  companyBrokerageValue: number // 物流公司佣金账户增加金额
  platformFreightValue: number // 平台运费账户增加金额
  platformProfitValue: number // 平台毛利账户增加金额
  platformBrokerageValue: number // 平台佣金账户增加金额
  waybillCodeSet: string // 运单号集合
  transferTime: Date // 划转时间
  transferUserId: number // 划转人id
}

// 物流公司划转记录 API
export const CompanyTransferLogApi = {
  // 查询物流公司划转记录分页
  getCompanyTransferLogPage: async (params: any) => {
    return await request.get({ url: `/wljh/company-transfer-log/page`, params })
  },

  // 查询物流公司划转记录详情
  getCompanyTransferLog: async (id: number) => {
    return await request.get({ url: `/wljh/company-transfer-log/get?id=` + id })
  },

  // 新增物流公司划转记录
  createCompanyTransferLog: async (data: CompanyTransferLogVO) => {
    return await request.post({ url: `/wljh/company-transfer-log/create`, data })
  },

  // 修改物流公司划转记录
  updateCompanyTransferLog: async (data: CompanyTransferLogVO) => {
    return await request.put({ url: `/wljh/company-transfer-log/update`, data })
  },

  // 删除物流公司划转记录
  deleteCompanyTransferLog: async (id: number) => {
    return await request.delete({ url: `/wljh/company-transfer-log/delete?id=` + id })
  },

  // 导出物流公司划转记录 Excel
  exportCompanyTransferLog: async (params) => {
    return await request.download({ url: `/wljh/company-transfer-log/export-excel`, params })
  }
}